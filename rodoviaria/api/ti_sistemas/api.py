import logging
import re
import uuid
from datetime import <PERSON><PERSON><PERSON>
from decimal import Decimal as D

from celery import shared_task
from faker import Faker
from pydantic import parse_obj_as
from tenacity import retry, retry_if_exception_type, stop_after_attempt

from commons.celery_utils import DefaultQueueNames
from commons.dateutils import now, to_tz
from commons.django_utils import error_str
from commons.redis import lock
from core.models_grupo import TrechoClasse as TrechoClasseBdjango
from rodoviaria.api.executors.impl import get_http_executor
from rodoviaria.api.forms import BuscarServicoForm, RetornoItinerario, ServicoForm
from rodoviaria.api.rodoviaria_api import RodoviariaAPI
from rodoviaria.api.ti_sistemas import descobrir_operacao, endpoints, models
from rodoviaria.api.ti_sistemas.exceptions import (
    TiSistemasAPIError,
    TiSistemasBlankReponseException,
    TiSistemasBloquearPoltronaException,
    TiSistemasCancelarCompraException,
    TiSistemasEfetuarCompraException,
)
from rodoviaria.api.ti_sistemas.memcache import TiSistemasMC
from rodoviaria.forms.cancela_rodoviaria_forms import CancelaVendaForm
from rodoviaria.forms.compra_rodoviaria_forms import BloquearPoltronasResponse, ComprarForm, VerificarPoltronaForm
from rodoviaria.forms.mapa_poltronas_forms import Assento, Deck, Onibus
from rodoviaria.models import TrechoClasse as TrechoClasseRodoviaria
from rodoviaria.models.core import Company, Passagem
from rodoviaria.models.ti_sistemas import RotaTiSistemas, TiSistemasLogin
from rodoviaria.service import class_match_svc, descobrir_rotas_ti_sistemas_async_svc
from rodoviaria.service.exceptions import (
    PoltronaJaSelecionadaException,
    RodoviariaOverbookingException,
)
from rodoviaria.service.poltronas_svc import seleciona_poltrona
from rodoviaria.service.solicita_cancelamento_svc import solicita_cancelamento_passagens

buserlogger = logging.getLogger("rodoviaria")

fake = Faker("pt_BR")


class TiSistemasAPI(RodoviariaAPI):
    Rota = RotaTiSistemas
    cache = TiSistemasMC()
    divergencia_maxima_pct = 50

    def __init__(self, company):
        super().__init__(company)
        self.login = TiSistemasLogin.objects.select_related("company").get(company=company)

    def __repr__(self):
        return f"{__class__.__name__}_{self.login.company.name}"

    def atualiza_origens(self):
        """A TI sistemas não disponibilizou um endpoint para atualização dos locais de embarque.
        Ao cadastrar uma nova empresa, é necessário executar fetch_rotas e fetch_trechos_vendidos a partir do
        postman para que os locais de embarque sejam criados.
        """
        return []

    def buscar_itinerario_request(self, company_external_id, id_viagem):
        executor = get_http_executor()
        request_config = endpoints.BuscarItinerarioConfig(self.login, company_external_id, id_viagem)
        response = request_config.invoke(
            executor,
        )
        response_json = response.json()
        return RetornoItinerario(
            raw=response_json,
            cleaned=response_json["list"],
            parsed=parse_obj_as(models.Itinerario, response.json()["list"]),
        )

    def buscar_itinerario(self, params):
        return self.buscar_itinerario_request(self.company.company_external_id, params["id_viagem"])

    def itinerario(self, external_id, datetime_ida=None):
        return self.buscar_itinerario_request(self.company.company_external_id, external_id)

    def descobrir_rotas_async(self, next_days, shift_days, queue_name, return_task_object, modelo_venda):
        return descobrir_rotas_ti_sistemas_async_svc.descobrir_rotas(
            self.login,
            self.company.company_internal_id,
            next_days,
            shift_days,
            queue_name,
            return_task_object,
        )

    def descobrir_operacao_async(self, next_days, shift_days, queue_name, return_task_object):
        return descobrir_operacao.descobrir_operacao(self.login, next_days, shift_days, queue_name, return_task_object)

    def buscar_corridas(self, request_params, match_params=None):
        corridas = self._buscar_corridas_request(request_params)

        corridas_form = self._make_corridas_form(corridas)
        if not match_params:
            found = bool(corridas)
            return BuscarServicoForm(found=found, servicos=corridas_form)

        d_args = self._find_match_corridas(corridas, request_params, **match_params)
        return self._parse_retorno_buscar_servico(**d_args)

    def _make_corridas_form(self, corridas):
        corridas_form = [
            ServicoForm(
                external_id=corrida.external_id,
                external_datetime_ida=corrida.datetime_ida,
                external_datetime_chegada=corrida.datetime_chegada,
                classe=corrida.classe,
                preco=corrida.preco,
                provider_data=corrida,
                external_company_id=corrida.company_external_id,
                vagas=corrida.vagas,
            )
            for corrida in corridas
        ]

        return corridas_form

    @retry(
        retry=retry_if_exception_type(TiSistemasBlankReponseException),
        reraise=True,
        stop=stop_after_attempt(2),
    )
    def _buscar_corridas_request(self, params):
        params = models.BuscarCorridasInput.parse_obj(params)
        executor = get_http_executor()
        request_config = endpoints.BuscarCorridasConfig(self.login)
        response = request_config.invoke(executor, params=params.dict(by_alias=True))
        corridas = parse_obj_as(list[models.Corrida], response.json())

        corridas = [c for c in corridas if c.company_external_id == self.company.company_external_id]
        return corridas

    def _find_match_corridas(self, corridas, request_params, datetime_ida, timezone, tipo_assento, *args, **kwargs):
        matches = []
        mismatches = []
        d_args = {}
        buser_class = tipo_assento

        for servico in corridas:
            api_class = servico.classe.lower()
            if self._match_datetime_ida_servico(datetime_ida, timezone, servico.datetime_ida) and (
                self._does_class_match(buser_class, api_class)
            ):
                matches.append(servico)
            else:
                mismatches.append(servico)

        if len(matches) == 1:
            matched_service = matches[0]
            d_args.update({"servico_encontrado": matched_service, "timezone": timezone})
            return d_args

        if len(matches) > 1:
            sorted_matches_by_diff_datetime_ida = sorted(
                matches,
                key=lambda k: (
                    self._get_diff_datetime_ida_in_minutes(datetime_ida, timezone, k.datetime_ida),
                    -k.vagas,
                ),
            )

            # quando mais de um match, tenta achar o que tenha match de classe
            # senão achar, retorna o mais proximo de horario
            matched_service = sorted_matches_by_diff_datetime_ida[0]
            for servico in sorted_matches_by_diff_datetime_ida:
                api_class = servico.classe.lower()
                if self._does_class_match(buser_class, api_class):
                    matched_service = servico
                    break

            d_args.update({"servico_encontrado": matched_service, "timezone": timezone})
            return d_args

        mismatches_classe_horario = [(m.classe, m.datetime_ida) for m in mismatches]
        msg = (
            f"Unmatch de servico {self.company.name} com {request_params}: "
            f"({buser_class}, {datetime_ida}, {timezone}) -> {mismatches_classe_horario}"
        )
        buserlogger.info(msg)
        d_args.update({"timezone": timezone, "mismatches": mismatches})

        return d_args

    def _parse_retorno_buscar_servico(
        self, servico_encontrado: models.Corrida = None, timezone=None, mismatches=None
    ) -> BuscarServicoForm:
        if mismatches is None:
            mismatches = []
        found = bool(servico_encontrado)
        if found:
            servico = ServicoForm(
                external_id=servico_encontrado.external_id,
                external_datetime_ida=to_tz(servico_encontrado.datetime_ida, timezone),
                external_datetime_chegada=to_tz(servico_encontrado.datetime_chegada, timezone),
                classe=servico_encontrado.classe,
                preco=servico_encontrado.preco,
                provider_data=servico_encontrado,
                external_company_id=servico_encontrado.company_external_id,
                vagas=servico_encontrado.vagas,
            )
            servicos = [servico]
        else:
            servicos = [self._normalizar_servico(s, timezone) for s in mismatches]

        return BuscarServicoForm(found=found, servicos=servicos)

    def _normalizar_servico(self, servico: models.Corrida, timezone):
        servico_form = ServicoForm(
            external_id=servico.external_id,
            preco=servico.preco,
            external_datetime_ida=to_tz(servico.datetime_ida, timezone),
            classe=servico.classe,
            external_company_id=servico.company_external_id,
            vagas=servico.vagas,
            provider_data=servico.dict(),
        )
        return servico_form

    @lock("compra_rodoviaria_{params.travel_id}", max_wait_time=0, except_timeout=True)
    def comprar(self, params: ComprarForm, from_add_pax=False):
        passagens = self._create_passagens(params)
        passagens_map_by_poltronas = {p.poltrona_external_id: p for p in passagens}
        passagens_confirmadas = []
        comprador = next((pax for pax in params.passageiros if pax.cpf), None)
        for poltrona, pax in zip(params.poltronas, params.passageiros):
            passagem = passagens_map_by_poltronas[poltrona]
            order_id = passagem.pedido_external_id
            seat_id = passagem.localizador
            try:
                self._efetuar_compra_request(
                    {
                        "seat_id": seat_id,
                        "order_id": order_id,
                        "nome": pax.name,
                        "documento": pax.rg_number[:20],
                        "comprador": {
                            "email": "<EMAIL>",
                            "nome": comprador.name if comprador else pax.name,
                            "cpf": self._get_cpf_comprador(comprador, pax),
                            "telefone": "1299999-9999",
                        },
                    }
                )
            except TiSistemasEfetuarCompraException as exc:
                solicita_cancelamento_passagens(passagens_confirmadas)
                async_desbloquear_poltronas.delay(self.company.id, params.trechoclasse_id, params.poltronas)
                passagem.save_error(error_str(exc))
                raise
            passagem.save_confirmed()
            passagens_confirmadas.append(passagem)

        return {"passagens": [p.to_dict_json() for p in passagens_map_by_poltronas.values()]}

    def _get_cpf_comprador(self, comprador, pax):
        return pax.buyer_cpf or (comprador.cpf if comprador and comprador.cpf else pax.cpf)

    def _create_passagens(self, params: ComprarForm):
        trecho_classe = self.get_active_trecho_classe(params.trechoclasse_id)
        passagens = []
        for poltrona, passageiro in zip(params.poltronas, params.passageiros):
            if params.extra_poltronas:
                bloqueio_poltrona = parse_obj_as(dict, params.extra_poltronas)
            else:
                bloqueio_poltrona = self._get_cached_poltrona_ou_bloqueia(params.trechoclasse_id, poltrona)
            passagens.append(
                Passagem(
                    trechoclasse_integracao=trecho_classe,
                    company_integracao=self.company,
                    buseiro_internal_id=passageiro.id,
                    poltrona_external_id=bloqueio_poltrona["seat"],
                    localizador=bloqueio_poltrona["seat_id"],
                    pedido_external_id=bloqueio_poltrona["order_id"],
                    status=Passagem.Status.INCOMPLETA,
                    valor_cheio=D(str(params.valor_cheio)),
                    travel_internal_id=params.travel_id,
                    preco_rodoviaria=D(str(trecho_classe.preco_rodoviaria)),
                )
            )
        return self.create_passagens(passagens)

    def cancela_venda(self, params: CancelaVendaForm):
        passagens = self.get_passagens_confirmadas(params.travel_id, params.buseiro_id)
        for passagem in passagens:
            order_id = passagem.pedido_external_id
            seat_id = passagem.localizador
            try:
                self._cancelar_compra_request({"order_id": order_id, "seat_id": seat_id})
            except (TiSistemasAPIError, TiSistemasCancelarCompraException) as exc:
                self._save_error_cancelamento_passagens(passagens, error_str(exc))
                raise
            else:
                passagem.save_canceled()

    @retry(
        retry=retry_if_exception_type(PoltronaJaSelecionadaException),
        reraise=True,
        stop=stop_after_attempt(3),
    )
    def verifica_poltrona(self, params: VerificarPoltronaForm):
        trecho_classe_id = params.trechoclasse_id
        poltronas_map = self.get_map_poltronas(trecho_classe_id)
        vagas_disponiveis = len([poltrona for poltrona, situacao in poltronas_map.items() if situacao == "livre"])
        if vagas_disponiveis < params.passageiros:
            raise RodoviariaOverbookingException(vagas_disponiveis=vagas_disponiveis, trechoclasse_id=trecho_classe_id)
        poltronas_indisponiveis = self.cache.get_poltrona_indisponivel_cache(trecho_classe_id)
        poltronas_selecionadas = seleciona_poltrona(
            poltronas_map, params.passageiros, poltronas_to_exclude=poltronas_indisponiveis
        )
        poltronas_selecionadas = sorted([int(p) for p in poltronas_selecionadas])

        self.bloquear_poltronas(trecho_classe_id, poltronas_selecionadas)
        return poltronas_selecionadas

    def get_timezone(self, trecho_classe: TrechoClasseRodoviaria):
        if trecho_classe.origem.cidade.timezone:
            return trecho_classe.origem.cidade.timezone

        tc_bdjango = TrechoClasseBdjango.objects.get(id=trecho_classe.trechoclasse_internal_id)
        return tc_bdjango.trecho_vendido.origem.cidade.timezone

    def get_map_poltronas(self, trecho_classe_id):
        trecho_classe = self.get_active_trecho_classe(trecho_classe_id)
        timezone = self.get_timezone(trecho_classe)
        mapa_viagem = self._mapa_viagem_request(
            {
                "date_ida": to_tz(trecho_classe.datetime_ida, timezone),
                "origem_external_id": trecho_classe.origem.id_external,
                "destino_external_id": trecho_classe.destino.id_external,
                "company_external_id": self.company.company_external_id,
                "viagem_id": trecho_classe.external_id,
            }
        )
        poltronas_map_api = mapa_viagem.layout_onibus.poltronas_map
        poltronas_map_db = self._muda_para_ocupado_poltronas_from_db(trecho_classe_id, poltronas_map_api)
        return poltronas_map_db

    def get_desenho_mapa_poltronas(self, trecho_classe_id: int) -> Onibus:
        trecho_classe = self.get_active_trecho_classe(trecho_classe_id)
        timezone = self.get_timezone(trecho_classe)
        mapa_viagem = self._mapa_viagem_request(
            {
                "date_ida": to_tz(trecho_classe.datetime_ida, timezone),
                "origem_external_id": trecho_classe.origem.id_external,
                "destino_external_id": trecho_classe.destino.id_external,
                "company_external_id": self.company.company_external_id,
                "viagem_id": trecho_classe.external_id,
            }
        )

        tipo_assento_buser = class_match_svc.get_buser_class_by_company(
            self.company, mapa_viagem.detalhes_viagem.classe
        )
        decks = []
        for deck_api in mapa_viagem.layout_onibus.decks:
            seats = []
            for assento_api in deck_api.poltronas:
                seats.append(
                    {
                        "livre": assento_api.status != "OCCUPIED",
                        "y": assento_api.x,
                        # mantem o padrao de layout onde o X == 3 indica o corredor
                        "x": assento_api.y + 1 if assento_api.y > 2 else assento_api.y,
                        "numero": assento_api.numero,
                        "tipo_assento": tipo_assento_buser,
                        "categoria_especial": [Passagem.CategoriaEspecial.NORMAL],
                    }
                )
            decks.append(Deck(andar=deck_api.numero, assentos=parse_obj_as(list[Assento], seats)))
        layout_onibus = Onibus(layout=decks)
        self._muda_map_para_ocupado_poltronas_from_db_v2(trecho_classe_id, layout_onibus)
        return layout_onibus

    def bloquear_poltronas(self, trecho_classe_id, poltronas):
        trecho_classe = self.get_active_trecho_classe(trecho_classe_id)
        timezone = self.get_timezone(trecho_classe)
        order_id = self._generate_order_id()
        try:
            bloqueio = self._bloquear_poltrona_request(
                {
                    "date_ida": to_tz(trecho_classe.datetime_ida, timezone),
                    "origem_external_id": trecho_classe.origem.id_external,
                    "destino_external_id": trecho_classe.destino.id_external,
                    "company_external_id": self.company.company_external_id,
                    "viagem_id": trecho_classe.external_id,
                    "order_id": order_id,
                    "seats": poltronas,
                }
            )
        except PoltronaJaSelecionadaException as exc:
            poltrona_selecionada = re.findall(r"\d+", exc.message)[0].zfill(2)
            self.cache.insert_poltrona_indisponivel_cache(trecho_classe_id, poltrona_selecionada)
            raise
        except TiSistemasBloquearPoltronaException:
            async_desbloquear_poltronas.delay(self.company.id, trecho_classe_id, poltronas)
            raise
        for poltrona in bloqueio.seats:
            cache_value = {"order_id": order_id, **poltrona.dict()}
            self.cache.set_poltrona_cache_key(trecho_classe_id, poltrona.seat, cache_value)

    def bloquear_poltronas_v2(self, trechoclasse_id, poltrona, categoria_especial) -> BloquearPoltronasResponse:
        self.bloquear_poltronas(trechoclasse_id, [poltrona])
        cache_payload = self.cache.get_poltrona_cache_key(trechoclasse_id, poltrona)
        return BloquearPoltronasResponse(
            seat=poltrona, best_before=now() + timedelta(minutes=20), external_payload=cache_payload
        )

    def desbloquear_poltronas(self, trecho_classe_id, poltronas):
        for poltrona in poltronas:
            bloqueio = self.cache.get_poltrona_cache_key(trecho_classe_id, poltrona)
            if bloqueio:
                poltronas_indisponiveis = self.cache.get_poltrona_indisponivel_cache(trecho_classe_id)
                self._desbloquear_poltrona_request({"order_id": bloqueio["order_id"], "seat_id": bloqueio["seat_id"]})
                self.cache.delete_poltrona_cache_key(trecho_classe_id, poltrona)
                poltronas_indisponiveis.discard(poltrona)
                self.cache.set_poltrona_indisponivel_cache(trecho_classe_id, poltronas_indisponiveis)

    def _validate_response_trechos_vendidos_request(self, response_json):
        """O retorno da API não é confiável, precisamos validar a consistência dos dados"""
        trechos, trechos_invalidos = [], []
        for trecho in response_json["list"]:
            if trecho["origemId"] and trecho["destinoId"] and trecho["origemId"] != trecho["destinoId"]:
                trechos.append(trecho)
            else:
                trechos_invalidos.append(trecho)

        if trechos_invalidos:
            buserlogger.warning("trechos_vendidos_invalidos", extra={"ids": [t["id"] for t in trechos_invalidos]})

        return trechos

    def buscar_trechos_vendidos_request(self, id_viagem):
        executor = get_http_executor()
        request_config = endpoints.BuscarTrechosVendidosConfig(
            self.login, company_external_id=self.company.company_external_id, id_viagem=id_viagem
        )
        response = request_config.invoke(executor)
        response_json = response.json()

        trechos_validos = self._validate_response_trechos_vendidos_request(response_json)
        trechos_parsed = parse_obj_as(list[models.BuscarTrechosVendidosOutput], trechos_validos)
        return trechos_parsed

    def buscar_trechos_vendidos(self, id_viagem):
        trechos_api = self.buscar_trechos_vendidos_request(id_viagem)
        return trechos_api

    def _generate_order_id(self, length=12):
        return int(uuid.uuid4().hex, 16) % 10**length

    def _get_cached_poltrona_ou_bloqueia(self, trechoclasse_id, poltrona):
        bloqueio = self.cache.get_poltrona_cache_key(trechoclasse_id, poltrona)
        if not bloqueio:
            self.bloquear_poltronas(trechoclasse_id, [poltrona])
            bloqueio = self.cache.get_poltrona_cache_key(trechoclasse_id, poltrona)
        return bloqueio

    def _mapa_viagem_request(self, params):
        params = models.MapaViagemInput.parse_obj(params)
        executor = get_http_executor()
        request_config = endpoints.MapaViagemConfig(self.login)
        response = request_config.invoke(executor, params=params.dict(by_alias=True))
        response_parsed = parse_obj_as(models.LayoutViagem, response.json())
        return response_parsed

    @retry(
        retry=retry_if_exception_type(TiSistemasBloquearPoltronaException),
        reraise=True,
        stop=stop_after_attempt(3),
    )
    def _bloquear_poltrona_request(self, params):
        params = models.BloquearPoltronaInput.parse_obj(params)
        executor = get_http_executor()
        request_config = endpoints.BloquearPoltronaConfig(self.login)
        response = request_config.invoke(executor, json=params.dict(by_alias=True))
        response_parsed = parse_obj_as(models.BloquearPoltronaOutput, response.json())
        return response_parsed

    @retry(
        retry=retry_if_exception_type(TiSistemasBlankReponseException),
        reraise=True,
        stop=stop_after_attempt(2),
    )
    def _desbloquear_poltrona_request(self, params):
        params = models.DesbloquearPoltronaInput.parse_obj(params)
        executor = get_http_executor()
        request_config = endpoints.DesbloquearPoltronaConfig(self.login)
        request_config.invoke(executor, params=params.dict(by_alias=True))
        return

    @retry(
        retry=retry_if_exception_type(TiSistemasEfetuarCompraException),
        reraise=True,
        stop=stop_after_attempt(3),
    )
    def _efetuar_compra_request(self, params):
        params = models.EfetuarCompraInput.parse_obj(params)
        executor = get_http_executor()
        request_config = endpoints.EfetuarCompraConfig(self.login)
        response = request_config.invoke(executor, json=params.dict(by_alias=True))
        response_parsed = parse_obj_as(models.EfetuarCompraOutput, response.json())
        return response_parsed

    def _consultar_reserva_request(self, params):
        params = models.ConsultarReservaInput.parse_obj(params)
        executor = get_http_executor()
        request_config = endpoints.ConsultarReservaConfig(self.login)
        response = request_config.invoke(executor, params=params.dict(by_alias=True))
        response_parsed = parse_obj_as(models.ConsultarReservaOutput, response.json())
        return response_parsed

    def _cancelar_compra_request(self, params):
        params = models.CancelarCompraInput.parse_obj(params)
        executor = get_http_executor()
        request_config = endpoints.CancelarCompraConfig(self.login)
        request_config.invoke(executor, json=params.dict(by_alias=True))
        return True


@shared_task(queue=DefaultQueueNames.DESBLOQUEAR_POLTRONAS)
def async_desbloquear_poltronas(company_rodoviaria_id, trecho_classe_id, poltronas):
    company = Company.objects.get(id=company_rodoviaria_id)
    api = TiSistemasAPI(company)
    api.desbloquear_poltronas(trecho_classe_id, poltronas)
    buserlogger.info(
        "TI Sistemas - Desbloqueando poltronas %s do trecho %s da empresa %s",
        poltronas,
        trecho_classe_id,
        company.company_internal_id,
    )
