import logging
import unicodedata
from datetime import datetime, timedelta
from decimal import Decimal as D

from commons import celery_utils
from commons.dateutils import timezone, to_tz
from commons.utils import strip_punctuation
from rodoviaria.forms.compra_rodoviaria_forms import BloquearPoltronasResponse, ComprarForm, VerificarPoltronaForm
from rodoviaria.forms.mapa_poltronas_forms import Onibus
from rodoviaria.forms.staff_forms import CheckPaxForm
from rodoviaria.models.core import Company, Passagem, Remanejamento, TrechoClasse
from rodoviaria.service.class_match_svc import does_class_match, match_exato_pela_tabela
from rodoviaria.service.exceptions import DivergenciaPrecoException, RodoviariaTrechoclasseNotFoundException

buserlogger = logging.getLogger("rodoviaria")


class RodoviariaAPI:
    Rota = None
    divergencia_maxima_pct = 50

    def __init__(self, company):
        self.company = company
        self.company_id = company.id

    @property
    def base_url(self):
        return self.company.url_base

    @property
    def queue_name(self):
        company_name = unicodedata.normalize("NFKC", self.company.name)
        q = f"bp_{strip_punctuation(company_name).replace(' - ', '_').replace(' ', '_')}"
        if self.company.modelo_venda == Company.ModeloVenda.HIBRIDO:
            q += "_hibrido"
        if not celery_utils.is_deployed_queue(q):
            return None
        return q

    def _make_request(self):
        raise NotImplementedError("Método _make_request não implementado")

    def _check_response(self):
        raise NotImplementedError("Método _check_response não implementado")

    def atualiza_origens(self):
        """
        Retorna a lista de Locais operados pela empresa

        Entrada: None

        Saida:
        Uma lista de rodoviaria.api.forms.Localidade
        """
        raise NotImplementedError("Método atualiza_origens não implementado")

    def cancela_venda(self, params):
        raise NotImplementedError("Método cancela_venda não implementado")

    def comprar(self, params, from_add_pax=None):
        raise NotImplementedError("Método comprar não implementado")

    def buscar_todos_servicos(self):
        raise NotImplementedError("Método buscar_todos_servicos não implementado")

    def buscar_servicos_por_data(self, data_inicio, data_fim):
        raise NotImplementedError("Método buscar_servicos_por_data não implementado")

    def buscar_itinerario(self, params):
        raise NotImplementedError("Método buscar_itinerario não implementado")

    def lista_cidades(self):
        raise NotImplementedError("Método lista_cidades não implementado")

    def _get_diff_datetime_ida_in_minutes(self, datetime_ida, timezone, datetime_ida_servico):
        return (to_tz(datetime_ida_servico, timezone) - datetime_ida).total_seconds() / 60

    def _match_datetime_ida_servico(self, datetime_ida, timezone, datetime_ida_servico, zero_tolerance=False):
        timediff = self._get_diff_datetime_ida_in_minutes(datetime_ida, timezone, datetime_ida_servico)
        tolerance = 30
        if self.company.modelo_venda == Company.ModeloVenda.HIBRIDO:
            tolerance = 1
        if zero_tolerance or self.company.has_feature(Company.Feature.ZERO_TOLERANCIA_MATCH_HORARIO):
            tolerance = 0
        datetime_ida_match = -1 <= timediff <= tolerance
        return datetime_ida_match

    def verifica_poltrona(self, params):
        raise NotImplementedError("Método verifica_poltrona não implementado")

    def vagas_por_categoria_especial(self, trechoclasse_id):
        raise NotImplementedError("Método verifica_poltrona não implementado")

    def bloquear_poltronas(self, trecho_classe_id, poltronas):
        raise NotImplementedError("Método bloquear_poltronas não implementado")

    def bloquear_poltronas_v2(self, trechoclasse_id, poltrona, categoria_especial) -> BloquearPoltronasResponse:
        raise NotImplementedError("Método bloquear_poltronas_v2 não implementado")

    def desbloquear_poltronas(self, trecho_classe_id, poltronas):
        raise NotImplementedError("Método desbloquear_poltronas não implementado")

    def add_multiple_pax_na_lista_passageiros_viagem(self, params):
        raise NotImplementedError(
            f"Método add_multiple_pax_na_lista_passageiros_viagem não implementado para empresa {self.company.name}"
        )

    def add_pax_na_lista_passageiros_viagem(self, params: CheckPaxForm):
        if self._is_passagem_emitida(params):
            return {"sucesso": True, "already_on_api": True}

        if self._has_remanejamento_pendente(params.travel_id):
            return {"sucesso": False, "warning": "Remanejamento será executado assíncronamente"}

        if hasattr(params, "poltrona") and params.poltrona:
            poltronas = [params.poltrona]
        else:
            poltronas = self.verifica_poltrona(
                VerificarPoltronaForm(trechoclasse_id=params.trechoclasse_id, passageiros=1)
            )
        self.comprar(
            ComprarForm.parse_obj(self._add_pax_params(params, poltronas)),
            from_add_pax=True,
        )
        return {"sucesso": True, "already_on_api": False}

    def _is_passagem_emitida(self, params: CheckPaxForm):
        pax_ja_emitido = Passagem.objects.filter(
            buseiro_internal_id=params.passenger.buseiro_id,
            travel_internal_id=params.travel_id,
            status__in=Passagem.STATUS_CONFIRMADA_LIST,
        ).exists()
        return pax_ja_emitido

    def _has_remanejamento_pendente(self, travel_id):
        remanejamento_pendente = Remanejamento.objects.filter(
            status=Remanejamento.Status.PENDENTE,
            travel_destino_internal_id=travel_id,
            created_at__gt=timezone.now() - timedelta(minutes=15),
        ).exists()
        return remanejamento_pendente

    def _add_pax_params(self, params: CheckPaxForm, poltronas):
        return {
            "trechoclasse_id": params.trechoclasse_id,
            "travel_id": params.travel_id,
            "valor_cheio": params.valor_por_buseiro,
            "poltronas": poltronas,
            "passageiros": [
                {
                    "buseiro_id": params.passenger.buseiro_id,
                    "id": params.passenger.buseiro_id,
                    "cpf": params.passenger.cpf,
                    "name": params.passenger.name,
                    "rg_number": params.passenger.rg_number,
                    "phone": params.passenger.phone,
                    "buyer_cpf": params.passenger.buyer_cpf,
                }
            ],
        }

    def dados_bpe_passagem(self, travel_id):
        raise NotImplementedError("Método dados_bpe_passagem não implementado")

    def get_trechos_vendidos(self, trecho_classe, rota):
        raise NotImplementedError("Método get_trechos_vendidos não implementado")

    def itinerario(self, external_id, datetime_ida=None):
        """
        Retorna o itinerario de uma viagem

        Entrada:
        external_id: str -> id que identifica a viagem
        datetime_ida: datetime -> data e hora de inicio da viagem

        Saida:
        É retornado `rodoviaria.api.base.ResponseWrapper` com o atributo `parsed` sendo uma lista de Paradas(BaseModel),
        com cada elemento contendo os atributos:

            datetime_ida: datetime -> data e hora de saida da parada
            tempo_embarque: int -> duração em segundos do tempo de embarque na parada atual
            duracao: int -> duracao em segundos da viagem entre a parada anterior e a atual
            distancia: int -> distância em kilometros entre a parada anterior e a atual
            local: Localidade(BaseModel) ->
                nome_cidade: str -> nome da cidade da parada
                external_local_id: str -> id externo do local de parada
                uf: str -> uf do estado da parada
                external_cidade_id: str -> id externo da cidade da parada
                descricao: str -> descrição do local de parada
                id_cidade_ibge: str -> codigo ibge da cidade da parada

        Além disso, `parsed` contém um atributo `hash` que resume e identifica o itinerario de acordo com as paradas
        e durações em um hashlib.md5

        """
        raise NotImplementedError("Método itinerario não implementado")

    def match_local_de_embarque(
        self,
        local_embarque_internal_id,
        cidade_internal_id,
        timezone,
        nome_cidade,
        codigo_ibge,
        company,
        company_internal_id,
    ):
        raise NotImplementedError("Método match_local_de_embarque não implementado")

    def rota_id_from_trecho_classe(self, trecho_classe):
        return None

    def datetime_ida_from_trecho_classe(self, trecho_classe):
        return trecho_classe.grupo.datetime_ida

    def rg_or_cpf(self, passageiro):
        if hasattr(passageiro, "cpf") and passageiro.cpf:
            return passageiro.cpf
        return passageiro.rg_number.replace("ã", "a")

    def _get_datetime_servico(self, timezone, datetime_servico, date_format):
        return to_tz(datetime.strptime(datetime_servico, date_format), timezone)

    def _sanitize_preco(self, preco):
        if isinstance(preco, str):
            preco = preco.replace(",", ".")
        return D(str(preco)) if preco else 0

    def _does_class_match(self, buser_class, api_class):
        return does_class_match(self.company, buser_class, api_class)

    def exact_class_match(self, buser_class, api_class):
        return match_exato_pela_tabela(self.company, buser_class, api_class)

    def get_passagens_confirmadas(self, travel_id, buseiro_id=None):
        base_qs = Passagem.objects.filter(
            travel_internal_id=travel_id, status__in=Passagem.STATUS_CONFIRMADA_LIST
        ).order_by("poltrona_external_id")

        if buseiro_id:
            base_qs = base_qs.filter(buseiro_internal_id=buseiro_id)
        return base_qs

    def get_active_trecho_classe(self, trecho_classe_id: int) -> TrechoClasse:
        try:
            trecho_classe = TrechoClasse.objects.select_related(
                "origem", "destino", "origem__cidade", "destino__cidade", "grupo"
            ).get(
                trechoclasse_internal_id=trecho_classe_id,
                active=True,
            )
        except TrechoClasse.DoesNotExist as exc:
            raise RodoviariaTrechoclasseNotFoundException(
                trechoclasse_id=trecho_classe_id,
                message="Trechoclasse não encontrado no banco de dados",
            ) from exc
        return trecho_classe

    def _save_error_passagens(self, passagens, erro):
        for passagem in passagens:
            if not passagem.erro:
                passagem.save_error(erro)

    def _save_error_cancelamento_passagens(self, passagens, erro):
        for passagem in passagens:
            if passagem.status == Passagem.Status.CONFIRMADA and not passagem.erro_cancelamento:
                passagem.save_error_cancelamento(erro)

    def _muda_para_ocupado_poltronas_from_db(self, trechoclasse_id, poltronas_map):
        poltronas_db = (
            Passagem.objects.select_related("trechoclasse_integracao")
            .filter(
                status=Passagem.Status.CONFIRMADA,
                trechoclasse_integracao__trechoclasse_internal_id=trechoclasse_id,
                trechoclasse_integracao__active=True,
            )
            .values_list("poltrona_external_id", flat=True)
        )
        for poltrona in poltronas_db:
            p = str(poltrona).zfill(2)
            if poltronas_map.get(p) == "livre":
                poltronas_map[p] = "ocupada"
                buserlogger.debug("%s: Poltrona %s ja confirmada no DB. Status: livre -> ocupada", self.company.name, p)
        return poltronas_map

    def _muda_map_para_ocupado_poltronas_from_db_v2(self, trechoclasse_id, layout_onibus: Onibus):
        poltronas_db = (
            Passagem.objects.select_related("trechoclasse_integracao")
            .filter(
                status=Passagem.Status.CONFIRMADA,
                trechoclasse_integracao__trechoclasse_internal_id=trechoclasse_id,
                trechoclasse_integracao__active=True,
            )
            .values_list("poltrona_external_id", flat=True)
        )
        for decks in layout_onibus.layout:
            for assento in decks.assentos:
                if assento.numero in poltronas_db:
                    assento.livre = False

    def cria_motorista(self, motorista):
        raise NotImplementedError("Método cria_motorista não implementado")

    def edita_dados_motorista(self, id_usuario, motorista):
        raise NotImplementedError("Método edita_dados_motorista não implementado")

    def edita_documentos_motorista(self, id_motorista, id_usuario_empresa, motorista):
        raise NotImplementedError("Método edita_documentos_motorista não implementado")

    def escala_motorista(self, id_usuario_empresa, id_itinerario):
        raise NotImplementedError("Método escala_motorista não implementado")

    def get_mapas_veiculos_api(self):
        raise NotImplementedError("Método get_mapas_veiculos_api não implementado")

    def get_veiculos_api(self):
        raise NotImplementedError("Método get_veiculos_api não implementado")

    def create_veiculos_api(self, veiculos):
        raise NotImplementedError("Método create_veiculos_api não implementado")

    def altera_veiculo_viagem(self, veiculo, andar, trechos_classe):
        raise NotImplementedError("Método altera_veiculo_viagem não implementado")

    def viagens_por_rota(self, rota_external_id):
        raise NotImplementedError("Método viagens_por_rota não implementado")

    def lista_reservas_viagem(self, grupo_classe_external_id):
        raise NotImplementedError("Método lista_reservas_viagem não implementado")

    def cancelar_reserva_por_localizador(self, localizador):
        raise NotImplementedError("Método cancelar_reserva_por_localizador não implementado")

    def descobrir_rotas_async(self, next_days, shift_days, queue_name, return_task_object, modelo_venda):
        raise NotImplementedError(
            f"Método descobrir_rotas_async não implementado para API {self.company.integracao.name}"
        )

    def descobrir_operacao_async(self, next_days, shift_days, queue_name, return_task_object):
        raise NotImplementedError(
            f"Método descobrir_operacao_async não implementado para API {self.company.integracao.name}"
        )

    def fetch_rotina(self, rota, next_days, first_day):
        raise NotImplementedError(f"Método fetch_rotina não implementado para API {self.company.integracao.name}")

    def fetch_rotina_async(self, rota, next_days, first_day, queue_name):
        raise NotImplementedError(f"Método fetch_rotina_async não implementado para API {self.company.integracao.name}")

    def fetch_rotinas_empresa(self, next_days, first_day, queue_name):
        raise NotImplementedError(
            f"Método fetch_rotinas_empresa não implementado para API {self.company.integracao.name}"
        )

    def inativar_grupo_classe(self, grupo_classe_external_id):
        raise NotImplementedError(
            f"Método inativar_grupo_classe não implementado para API {self.company.integracao.name}"
        )

    def update_bpe_passagem(self, passagem):
        raise NotImplementedError(
            f"Método update_bpe_passagem não implementado para API {self.company.integracao.name}"
        )

    def cancelar_reservas_por_pedido_id(self, pedido_id):
        raise NotImplementedError(
            f"Método cancelar_reservas_por_pedido_id não implementado para API {self.company.integracao.name}"
        )

    def fetch_data_limite_servicos(self, data_inicio, next_days=30, map_servicos_data=None):
        if map_servicos_data is None:
            map_servicos_data = {}
        data_fim = data_inicio + timedelta(days=next_days)

        new_map_servicos_data = self.buscar_servicos_por_data(data_inicio, data_fim)
        if not new_map_servicos_data:
            return map_servicos_data

        for serv in new_map_servicos_data:
            map_servicos_data[serv] = max(new_map_servicos_data[serv], map_servicos_data.get(serv, datetime.min))

        return self.fetch_data_limite_servicos(data_fim, next_days, map_servicos_data)

    def get_atualizacao_passagem_api_parceiro(self, passagem):
        raise NotImplementedError(
            f"Método de consultar informações na API desse parceiro ({self.company.integracao.name}) ainda não foi"
            f" implementada - {self.company.name} "
        )

    def cidades_destino(self, origem_external_id):
        raise NotImplementedError(f"Método cidades_destino não implementado para API {self.company.integracao.name}")

    def map_cidades_destinos(self):
        raise NotImplementedError(
            f"Método map_cidades_destinos não implementado para API {self.company.integracao.name}"
        )

    def get_desenho_mapa_poltronas(self, trecho_classe_id):
        raise NotImplementedError(
            f"Método get_desenho_mapa_poltronas não implementado para API {self.company.integracao.name}"
        )

    def create_passagens(self, passagens: list[Passagem]):
        erro = None
        if self.divergencia_maxima_pct >= 0:
            passagens, erro = self.update_passagens_on_divergencia(passagens)

        Passagem.objects.bulk_create(passagens)

        if erro:
            raise erro

        return passagens

    def update_passagens_on_divergencia(self, passagens: list[Passagem]) -> list[Passagem]:
        erro = None
        for passagem in passagens:
            divergencia = passagem.preco_rodoviaria - passagem.valor_cheio
            if divergencia <= D(str(self.divergencia_maxima_pct / 100.0)) * passagem.preco_rodoviaria:
                continue

            erro = DivergenciaPrecoException(
                f"Divergência de preço. travel_internal_id={passagem.travel_internal_id}, "
                f"buseiro_internal_id={passagem.buseiro_internal_id}"
            )
            for passagem in passagens:
                passagem.status = Passagem.Status.ERRO
                passagem.erro = erro.message
            break

        return passagens, erro
