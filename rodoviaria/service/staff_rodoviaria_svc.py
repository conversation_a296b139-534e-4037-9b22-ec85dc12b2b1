import logging

from celery import shared_task

from commons.celery_utils import DefaultQueueNames
from rodoviaria.api.orchestrator import OrchestrateRodoviaria
from rodoviaria.models.core import Company, Grupo, Passagem, TrechoClasse
from rodoviaria.serializers.serializer_passagem import PassagemBasicSerializer
from rodoviaria.service import rota_svc
from rodoviaria.service.compra_rodoviaria_svc import CompraRodoviariaSVC
from rodoviaria.service.exceptions import RodoviariaBaseException

buserlogger = logging.getLogger("rodoviaria")


class StaffRodoviariaSVC:
    def __init__(self, company_id=None, modelo_venda=None):
        self.rodoviaria = OrchestrateRodoviaria(company_id, modelo_venda)

    def lista_passageiros_viagem(self, travels_ids):
        lista_passagens_rodoviaria = Passagem.objects.filter(
            status__in=Passagem.STATUS_CONFIRMADA_LIST, travel_internal_id__in=travels_ids
        )
        lista_passagens_rodoviaria = lista_passagens_rodoviaria.to_serialize(PassagemBasicSerializer)
        return [passagem.serialize() for passagem in lista_passagens_rodoviaria]

    def itinerario(self, buser_grupo_id):
        grupo = Grupo.objects.select_related("company_integracao").filter(grupo_internal_id=buser_grupo_id).first()
        if not grupo:
            return None
        company = grupo.company_integracao
        return rota_svc.itinerario(
            OrchestrateRodoviaria(company.company_internal_id, company.modelo_venda),
            buser_grupo_id,
        )

    def empresas(self, features):
        return self.rodoviaria.empresas(features)

    def get_trechos_vendidos(self, grupo):
        return self.rodoviaria.get_trechos_vendidos(grupo)


@shared_task(queue=DefaultQueueNames.DESBLOQUEAR_POLTRONAS)
def async_desbloquear_poltrona(trecho_classe_id, poltronas):
    from rodoviaria.forms.compra_rodoviaria_forms import DesbloquearPoltronasForm

    form = DesbloquearPoltronasForm(trechoclasse_id=trecho_classe_id, poltronas=poltronas)
    try:
        CompraRodoviariaSVC(form).desbloquear_poltronas(form)
    except (RodoviariaBaseException, NotImplementedError):
        pass


def is_empresa_integrada(company_internal_id, modelo_venda):
    if not company_internal_id:
        return False
    return Company.objects.filter(
        company_internal_id=company_internal_id,
        modelo_venda=modelo_venda,
        features__contains=[Company.Feature.ACTIVE],
    ).exists()


def is_trechoclasse_integrado(trechoclasse_internal_id):
    return TrechoClasse.objects.filter(trechoclasse_internal_id=trechoclasse_internal_id, active=True).exists()
