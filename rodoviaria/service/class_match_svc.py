import logging
from datetime import datetime
from warnings import warn

from django.db.models import Q

from commons.django_utils import paginator_sort_by
from commons.memoize import memoize_with_log
from rodoviaria.models.core import Company, TipoAssento

buserlogger = logging.getLogger("rodoviaria")
CLASSE_NAO_MAPEADA = "classe_nao_mapeada"


def _match_semi_leito(api_class):
    return "semi" in api_class and "leito" in api_class


def _match_leito_individual(api_class):
    return api_class in [
        "leito individual",
        "leito_individual",
        "leito_especial",
        "leito - individual",
        "space individual semi-leito",
    ]


def _match_leito(api_class):
    return api_class in [
        "leito",
        "leito duplo",
        "leito total",
        "leito total.",
        "leito total - non-stop",
        "leito total - conjugado",
        "dd - leito",
        "double deck - leito",
        "leito com ar condicionado",
        "space semi-leito",
    ]


def _match_cama_premium(api_class):
    return api_class in ["cama premium", "cama_premium", "leito_cama_especial_1"]


def _match_cama_premium_individual(api_class):
    return api_class in ["cama premium individual", "leito_cama_especial_2"]


def _match_leito_cama(api_class):
    return api_class in ["leito cama", "leito_cama", "leito_especial", "cama"] or (
        "leito" in api_class and "cama" in api_class
    )


def _match_leito_cama_individual(api_class):
    return api_class in [
        "leito_cama_individual",
        "leito-cama individual",
        "leito cama individual",
    ] or ("cama" in api_class and "individual" in api_class)


def _match_executivo(api_class):
    return (
        "executivo" in api_class
        or "exec" in api_class
        or ("conv" in api_class and "plus" in api_class)
        or ("double" in api_class and "deck" in api_class and "leito" not in api_class)
    )


def _match_convencional(api_class):
    return "convencional" in api_class or (
        "conv" in api_class and "plus" not in api_class and "decker bus" not in api_class
    )


CLASS_MATCH = {
    "convencional": _match_convencional,
    "executivo": _match_executivo,
    "leito individual": _match_leito_individual,
    "leito": _match_leito,
    "semi leito": _match_semi_leito,
    "cama premium": _match_cama_premium,
    "cama premium individual": _match_cama_premium_individual,
    "leito cama": _match_leito_cama,
    "leito cama individual": _match_leito_cama_individual,
}


def buser_class(api_class):
    warn(
        "DEPRECATED",
        DeprecationWarning,
        stacklevel=2,
    )
    if not api_class:
        return ""
    api_class = api_class.lower()
    for classe in CLASS_MATCH:
        if CLASS_MATCH[classe](api_class):
            return classe
    return CLASSE_NAO_MAPEADA


def _does_class_match_by_name(buser_class, api_class):
    warn(
        "DEPRECATED",
        DeprecationWarning,
        stacklevel=2,
    )
    if not api_class:
        return True
    api_class = api_class.lower()
    match_function = CLASS_MATCH.get(buser_class)
    if not match_function:
        return buser_class in api_class
    return match_function(api_class)


def regras_excecao(company_internal_id, modelo_venda, _buser_class, api_class):
    """
    Regras de exceção de match de classe para casos que a empresa parceira
    não pode vender, segundo a ANTT, o que de fato está vendendo na ponta:
    ex.:
        na API está LEITO CAMA, mas na realidade é CAMA PREMIUM
    Se não fizermos isso, teremos perda considerável de GMV.
    Usar essa funcionalidade alinhado com o time comercial.
    """
    warn(
        "DEPRECATED",
        DeprecationWarning,
        stacklevel=2,
    )
    _buser_class = _buser_class.lower()
    api_class = api_class.lower()
    if modelo_venda == "marketplace":
        # Expresso Adamantina
        if company_internal_id == 282 and _buser_class == "semi leito":
            return True

        # Luxor Turismo & Levare
        if company_internal_id in (13, 44):
            if _buser_class == "cama premium individual" and "individual" in api_class:
                return True
            if _buser_class == "cama premium" and "individual" not in api_class:
                return True

        # Primar Turismo
        if (
            company_internal_id == 70
            and api_class == "convencional"
            and (_buser_class == "semi leito" or _buser_class == "executivo")
        ):
            return True

        # Guerino Seiscento
        if company_internal_id == 445:
            if _buser_class == "executivo":
                return "executivo" in api_class
            if _buser_class == "convencional":
                return "convencional" in api_class

        # Soares Turismo
        if (
            company_internal_id == 134
            and ("leito" in api_class and "cama" in api_class)
            and _buser_class == "cama premium"
        ):
            return True

        # Itapemirim & Catedral
        formatted_api_class = buser_class(api_class)
        if company_internal_id in (1190, 6247) and formatted_api_class == "executivo" and _buser_class == "semi leito":
            return True

        # Regra de exceção genérica sobre classes convencionais
        if _buser_class == "executivo" and "convencional" in api_class:
            return True

    return False


def _filter_by_search_fields(
    queryset, search_company, search_tipo_assento, search_not_linked_only, search_active_companies_only
):
    if search_company:
        queryset = queryset.filter(company__name__icontains=search_company)
    if search_tipo_assento:
        queryset = queryset.filter(
            Q(tipo_assento_parceiro__icontains=search_tipo_assento)
            | Q(tipo_assento_buser_preferencial__icontains=search_tipo_assento)
            | Q(tipos_assentos_buser_match__icontains=search_tipo_assento)
        )
    if search_not_linked_only:
        queryset = queryset.filter(tipo_assento_buser_preferencial__isnull=search_not_linked_only)
    if search_active_companies_only:
        queryset = queryset.filter(company__features__contains=[Company.Feature.ACTIVE])
    return queryset


def listar_tipos_assentos(params):
    query_set = (
        TipoAssento.objects.all()
        .order_by("company__name", "tipo_assento_parceiro", "tipo_assento_buser_preferencial")
        .values(
            "id",
            "company__name",
            "tipo_assento_parceiro",
            "tipo_assento_buser_preferencial",
            "tipos_assentos_buser_match",
        )
    )

    query_set = _filter_by_search_fields(
        query_set,
        params.search_company,
        params.search_tipo_assento,
        params.search_not_linked_only,
        params.search_active_companies_only,
    )

    if params.has_pagination:
        query_set, count, num_pages = paginator_sort_by(
            query_set,
            params.paginator.sort_by,
            params.paginator.rows_per_page,
            params.paginator.page,
            params.paginator.descending,
        )
        return list(query_set), count, num_pages
    return list(query_set), None, None


def linkar_tipo_assento(id_assento, tipo_assento_buser_preferencial, tipos_assentos_buser_match):
    ta = TipoAssento.objects.get(pk=id_assento)
    ta.tipo_assento_buser_preferencial = tipo_assento_buser_preferencial
    ta.tipos_assentos_buser_match = tipos_assentos_buser_match
    ta.updated_at = datetime.now()
    ta.save()


def get_buser_class_by_trecho_vendido(trecho_vendido):
    if trecho_vendido.tipo_assento and trecho_vendido.tipo_assento.tipo_assento_buser_preferencial:
        return trecho_vendido.tipo_assento.tipo_assento_buser_preferencial

    tipo_assento_buser = buser_class(trecho_vendido.classe)
    buserlogger.info(
        "[CLASS_MATCH] Tipo assento não registrado para TrechoVendido",
        extra={
            "trecho_vendido_id": trecho_vendido.id,
            "tipo_assento_parceiro": trecho_vendido.classe,
            "tipo_assento_buser_preferencial": tipo_assento_buser,
        },
    )

    return tipo_assento_buser


def get_buser_class_by_company_internal_id(company_internal_id, modelo_venda, tipo_assento_parceiro):
    company = Company.objects.get(company_internal_id=company_internal_id, modelo_venda=modelo_venda)
    return get_buser_class_by_company(company, tipo_assento_parceiro)


def get_buser_class_by_company(company, tipo_assento_parceiro):
    tipo_assento_parceiro = tipo_assento_parceiro or ""
    ta = TipoAssento.objects.filter(
        company=company,
        tipo_assento_parceiro__in=[tipo_assento_parceiro, tipo_assento_parceiro.lower(), tipo_assento_parceiro.upper()],
    ).first()

    if ta and ta.tipo_assento_buser_preferencial:
        return ta.tipo_assento_buser_preferencial

    tipo_assento_buser = buser_class(tipo_assento_parceiro)
    buserlogger.info(
        "[CLASS_MATCH] Tipo assento não registrado",
        extra={
            "tipo_assento_parceiro": tipo_assento_parceiro,
            "tipo_assento_buser_preferencial": tipo_assento_buser,
        },
    )

    return tipo_assento_buser


def does_class_match(company, tipo_assento_buser, tipo_assento_parceiro):
    if _does_class_match_pela_tabela(company, tipo_assento_buser, tipo_assento_parceiro):
        return True

    matched = _does_class_match_por_nome_ou_regra_excecao(company, tipo_assento_buser, tipo_assento_parceiro)
    buserlogger.info(
        "[CLASS_MATCH] Tipo assento não registrado para match de classe",
        extra={
            "tipo_assento_parceiro": tipo_assento_parceiro,
            "tipo_assento_buser_tentativa_match": tipo_assento_buser,
            "matched": matched,
        },
    )

    return matched


def match_exato_pela_tabela(company, tipo_assento_buser, tipo_assento_parceiro):
    ta, _ = TipoAssento.objects.get_or_create(
        company=company,
        tipo_assento_parceiro=tipo_assento_parceiro,
    )
    return tipo_assento_buser == ta.tipo_assento_buser_preferencial


def _does_class_match_pela_tabela(company, tipo_assento_buser, tipo_assento_parceiro):
    ta, _ = TipoAssento.objects.get_or_create(
        company=company,
        tipo_assento_parceiro=tipo_assento_parceiro,
    )
    if ta.tipo_assento_buser_preferencial or ta.tipos_assentos_buser_match:
        return tipo_assento_buser == ta.tipo_assento_buser_preferencial or (
            ta.tipos_assentos_buser_match and tipo_assento_buser in ta.tipos_assentos_buser_match
        )
    return False


def _does_class_match_por_nome_ou_regra_excecao(company, tipo_assento_buser, tipo_assento_parceiro):
    if regras_excecao(
        company.company_internal_id,
        company.modelo_venda,
        tipo_assento_buser,
        tipo_assento_parceiro,
    ):
        return True
    return _does_class_match_by_name(tipo_assento_buser, tipo_assento_parceiro)


@memoize_with_log(timeout=60 * 60)
def get_map_tipo_assento_empresa(company_id):
    tipos_assentos = TipoAssento.objects.filter(company_id=company_id, tipo_assento_buser_preferencial__isnull=False)
    return {ta.tipo_assento_parceiro: ta.tipo_assento_buser_preferencial for ta in tipos_assentos}
