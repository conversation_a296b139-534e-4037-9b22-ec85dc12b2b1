import logging
from collections import Counter
from datetime import timedelta

from django.db.models import Q
from django.utils.timezone import now
from zoneinfo import ZoneInfo

from commons.celery_utils import DefaultQueueNames
from commons.django_utils import error_str
from core.models_grupo import TrechoClasse
from rodoviaria import models as rodoviaria_models
from rodoviaria.api.orchestrator import OrchestrateRodoviaria
from rodoviaria.forms.compra_rodoviaria_forms import (
    BloquearPoltronasForm,
    BloquearPoltronasFormV2,
    BloquearPoltronasResponse,
    ComprarForm,
    DefaultForm,
    DesbloquearPoltronasForm,
    VerificarPoltronaForm,
)
from rodoviaria.forms.staff_forms import CheckPaxForm, CheckPaxMultipleForm
from rodoviaria.models.core import Company, Passagem
from rodoviaria.service import novos_modelos_svc
from rodoviaria.service.exceptions import RodoviariaException
from rodoviaria.service.grupo_trechoclasse_factory import GrupoTrechoClasseFactory, get_trechoclasse_from_buser_django
from rodoviaria.service.status_integracao_svc import update_trecho_classe_link_task

EMISSAO_PASSAGEM_LOG_TAG = "[EMISSAO PASSAGEM]"
buserlogger = logging.getLogger("rodoviaria")


class CompraRodoviariaSVC:
    def __init__(self, form: DefaultForm):
        self.trechoclasse_from_buser_django = None
        self.trechoclasse_id = form.trechoclasse_id
        self.travel_id = form.travel_id
        self.grupo = self._get_grupo(form.force_renew_link)
        self.company_id = self._get_company_id()
        self.modelo_venda = self._get_modelo_venda()
        self.rodoviaria = OrchestrateRodoviaria(self.company_id, self.modelo_venda)

    def _get_grupo(self, force_renew_link: bool) -> rodoviaria_models.TrechoClasse:
        try:
            trecho_classe = rodoviaria_models.TrechoClasse.objects.select_related(
                "grupo", "grupo__company_integracao"
            ).get(trechoclasse_internal_id=self.trechoclasse_id, active=True)

            nao_tem_locais = not trecho_classe.origem or not trecho_classe.destino
            trecho_desatualizado = (
                trecho_classe.grupo.company_integracao.modelo_venda == Company.ModeloVenda.MARKETPLACE
                and trecho_classe.updated_at < (now() - timedelta(minutes=5))
            )

            if nao_tem_locais or trecho_desatualizado or force_renew_link:
                trecho_classe.active = False
                trecho_classe.save()
                return self._tenta_criar_grupo_rodoviaria()

            return trecho_classe.grupo
        except rodoviaria_models.TrechoClasse.DoesNotExist:
            return self._tenta_criar_grupo_rodoviaria()

    def _tenta_criar_grupo_rodoviaria(self):
        internal_grupo = self._get_internal_grupo()
        company = self._get_company(internal_grupo)

        try:
            self.trechoclasse_from_buser_django = get_trechoclasse_from_buser_django(self.trechoclasse_id)
            grupo_trecho_classe = GrupoTrechoClasseFactory(
                company,
                self.trechoclasse_from_buser_django,
                OrchestrateRodoviaria(company.company_internal_id, company.modelo_venda),
            ).create()
            msg = f"O grupo/trecho_classe foi criado {self.trechoclasse_id}"
            buserlogger.info(msg)
            return grupo_trecho_classe.grupo
        except Exception as ex:
            msg = error_str(ex)
            buserlogger.info(msg)
            raise ex

    def _get_internal_grupo(self) -> TrechoClasse:
        try:
            return TrechoClasse.objects.get(id=self.trechoclasse_id).grupo
        except TrechoClasse.DoesNotExist as exc:
            raise RodoviariaException("Marketplace group is not registered in the buser_rodoviaria database") from exc

    def _get_company(self, internal_grupo):
        company_internal_id = internal_grupo.company_id
        company_modelo_venda = internal_grupo.modelo_venda
        if internal_grupo.modelo_venda == Company.ModeloVenda.HIBRIDO:
            company_internal_id = novos_modelos_svc.get_company_internal_id_integrado_por_rotina(
                company_internal_id, internal_grupo.rotina_onibus_id
            )
        try:
            return rodoviaria_models.Company.objects.get(
                Q(features__contains=[rodoviaria_models.Company.Feature.ACTIVE])
                | Q(features__contains=[rodoviaria_models.Company.Feature.ADD_PAX_STAFF]),
                company_internal_id=company_internal_id,
                modelo_venda=company_modelo_venda,
            )
        except rodoviaria_models.Company.DoesNotExist as exc:
            raise RodoviariaException("Marketplace company is not registered in the buser_rodoviaria database") from exc

    def _get_company_id(self):
        return self.grupo.company_integracao.company_internal_id

    def _get_modelo_venda(self):
        return self.grupo.company_integracao.modelo_venda

    def _get_timediff_partida(self, trechoclasse_internal_id):
        trechoclasse = rodoviaria_models.TrechoClasse.objects.get(trechoclasse_internal_id=trechoclasse_internal_id)
        origem_tz = trechoclasse.origem.cidade.timezone if trechoclasse.origem.cidade.timezone else "America/Sao_Paulo"
        datetime_partida = trechoclasse.datetime_ida.astimezone(ZoneInfo(origem_tz))
        timediff = round((datetime_partida - now()).total_seconds() / 3600)
        return timediff

    def efetua_compra(self, params: ComprarForm):
        buserlogger.info("%s efetua_compra params.travel_id=%s", EMISSAO_PASSAGEM_LOG_TAG, params.travel_id)

        paxs_emitidos_anteriormente = self.get_paxs_ja_emitidos(params)

        params.passageiros = [
            pax
            for pax in params.passageiros
            if pax.id not in {pax_emitido.buseiro_internal_id for pax_emitido in paxs_emitidos_anteriormente}
        ]

        response_paxs_emitidos_agora = None
        if params.passageiros:
            response_paxs_emitidos_agora = self.rodoviaria.comprar(params)
        paxs_emitidos_agora = response_paxs_emitidos_agora.get("passagens", []) if response_paxs_emitidos_agora else []

        self._dispara_atualizacao_trecho(params.trechoclasse_id)
        return {"passagens": paxs_emitidos_agora + [pax.to_dict_json() for pax in paxs_emitidos_anteriormente]}

    def get_paxs_ja_emitidos(self, params: ComprarForm):
        paxs_emitidos = Passagem.objects.filter(
            buseiro_internal_id__in=[pax.id for pax in params.passageiros],
            travel_internal_id=params.travel_id,
            status__in=Passagem.STATUS_CONFIRMADA_LIST,
        )

        paxs_sem_todas_conexoes = set()
        if paxs_emitidos and bool(paxs_emitidos.first().trechoclasse_integracao.conexao):
            count_pax = Counter(pax.buseiro_internal_id for pax in paxs_emitidos)
            paxs_sem_todas_conexoes = {pax_id for pax_id, count in count_pax.items() if count < 2}

        return [pax for pax in paxs_emitidos if pax.buseiro_internal_id not in paxs_sem_todas_conexoes]

    def _dispara_atualizacao_trecho(self, trecho_classe_internal_id):
        if self.rodoviaria.provider.company == Company.ModeloVenda.HIBRIDO:
            return
        if not self.trechoclasse_from_buser_django:
            self.trechoclasse_from_buser_django = get_trechoclasse_from_buser_django(trecho_classe_internal_id)
        update_trecho_classe_link_task.s(
            self.rodoviaria.provider.company.id, self.trechoclasse_from_buser_django.json()
        ).set(queue=DefaultQueueNames.POS_COMPRA_UPDATE_PRICE).apply_async()

    def verifica_poltrona(self, form: VerificarPoltronaForm):
        return self.rodoviaria.verifica_poltrona(form)

    def vagas_por_categoria_especial(self, form: DefaultForm):
        assentos_por_categoria = self.rodoviaria.vagas_por_categoria_especial(self.trechoclasse_id)
        return {"categorias_especiais": self._serializa_assentos_por_categoria(assentos_por_categoria)}

    def _serializa_assentos_por_categoria(self, assentos_por_categoria: list[dict]) -> list[dict]:
        """recebe um dict {"idoso_100": 1} e retorna um dict no formato
         {
          nome: 'Idoso 100%',
          vagas: 1,
          id: 'IDOSO_100'
        }
        """
        categoria = Passagem.CategoriaEspecial

        result = [
            {"id": tipo.value, "nome": tipo.label, "vagas": assentos_por_categoria.get(tipo.value, 0)}
            for tipo in categoria
        ]
        return result

    def desbloquear_poltronas(self, form: DesbloquearPoltronasForm):
        return self.rodoviaria.desbloquear_poltronas(self.trechoclasse_id, form.poltronas)

    def bloquear_poltronas(self, form: BloquearPoltronasForm):
        return self.rodoviaria.bloquear_poltronas(self.trechoclasse_id, form.poltronas)

    def bloquear_poltronas_v2(self, form: BloquearPoltronasFormV2) -> BloquearPoltronasResponse:
        return self.rodoviaria.bloquear_poltronas_v2(self.trechoclasse_id, form.poltrona, form.categoria_especial)

    def add_pax_na_lista_passageiros_viagem(self, form: CheckPaxForm):
        buserlogger.info(
            "%s add pax params.travel_id=%s e params.passenger.buseiro_id=%s",
            EMISSAO_PASSAGEM_LOG_TAG,
            form.travel_id,
            form.passenger.buseiro_id,
        )
        return self.rodoviaria.add_pax_na_lista_passageiros_viagem(form)

    def add_multiple_pax_na_lista_passageiros_viagem(self, form: CheckPaxMultipleForm):
        pax_para_emitir = []
        for travel in form.travels:
            for buseiro in travel.buseiros:
                pax_para_emitir.append(f"(travel_id={travel.travel_id}, buseiro_id={buseiro.id})")
        buserlogger.info("%s add multiple pax %s", EMISSAO_PASSAGEM_LOG_TAG, pax_para_emitir)
        return self.rodoviaria.add_multiple_pax_na_lista_passageiros_viagem(form)

    def get_desenho_mapa_poltronas(self, form: DefaultForm):
        return self.rodoviaria.get_desenho_mapa_poltronas(self.trechoclasse_id)

    def has_marcacao_assento(self, form: DefaultForm):
        trecho_classe = rodoviaria_models.TrechoClasse.objects.get(
            trechoclasse_internal_id=self.trechoclasse_id, active=True
        )
        return not trecho_classe.is_conexao
