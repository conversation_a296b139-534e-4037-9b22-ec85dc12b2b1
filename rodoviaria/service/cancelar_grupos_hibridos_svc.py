from datetime import timedelta

from beeline import traced
from celery import chain, group, shared_task
from celery.utils.log import get_task_logger
from django.utils import timezone

from commons.celery_utils import DefaultQueueNames, DefaultRateLimits
from rodoviaria.api.orchestrator import OrchestrateRodoviaria
from rodoviaria.models.core import Company, GrupoClasse, Passagem, TrechoClasse
from rodoviaria.models.vexado import VexadoGrupoClasse
from rodoviaria.service import cancela_passagens_pendentes_svc

task_logger = get_task_logger(__name__)

DEFAULT_MODELO_VENDA = Company.ModeloVenda.HIBRIDO


@traced("cancelar_grupos_hibridos_svc.cancelar_grupos_classe")
def cancelar_grupos_classe(company_id, grupos_classes_internal_ids, call_task_cancelar=True):
    grupos_classe = get_grupos_classe(company_id, grupos_classes_internal_ids)
    vexado_grupos_classe_to_update = []
    trechos_classe_to_update = []
    grupo_classe_id_map = {}
    vexado_grupos_classe_ja_cancelados = []
    for gc in grupos_classe:
        for vgc in gc.vexadogrupoclasse_set.all():
            if VexadoGrupoClasse.Status.CANCELADO not in vgc.status:
                grupo_classe_id_map[vgc.grupo_classe_external_id] = vgc
                vgc.status = VexadoGrupoClasse.Status.CANCELADO
                vexado_grupos_classe_to_update.append(vgc)
                for tc in gc.trechoclasse_set.all():
                    trechos_classe_to_update.append(tc)
                continue
            vexado_grupos_classe_ja_cancelados.append(vgc)
    if not vexado_grupos_classe_to_update and not grupos_classe:
        return None, None
    passagens_para_cancelar = Passagem.objects.filter(
        status__in=Passagem.STATUS_CONFIRMADA_LIST,
        trechoclasse_integracao__external_id__in=grupo_classe_id_map.keys(),
        trechoclasse_integracao__grupo__company_integracao__company_internal_id=company_id,
        trechoclasse_integracao__grupo__company_integracao__modelo_venda=DEFAULT_MODELO_VENDA,
        trechoclasse_integracao__datetime_ida__gt=timezone.now() + timedelta(hours=3),
    )
    cancelar_passagens_task = cancela_passagens_e_inativar_trechos_task.s(
        list(passagens_para_cancelar.values_list("id", flat=True)),
        [tc.id for tc in trechos_classe_to_update],
    )

    inativar_grupos_tasks = inativar_grupos_classe_group_tasks(company_id, grupo_classe_id_map.keys())

    _dispara_tasks(call_task_cancelar, cancelar_passagens_task, inativar_grupos_tasks)

    VexadoGrupoClasse.objects.bulk_update(vexado_grupos_classe_to_update, ["status"])  # não possui updated_at
    return (
        vexado_grupos_classe_to_update or list(vexado_grupos_classe_ja_cancelados),
        [tc.id for tc in trechos_classe_to_update],
    )


def get_grupos_classe(company_id, grupos_classes_internal_ids):
    grupos_classe = GrupoClasse.objects.prefetch_related("vexadogrupoclasse_set").filter(
        grupoclasse_internal_id__in=grupos_classes_internal_ids,
        grupo__company_integracao__company_internal_id=company_id,
        grupo__company_integracao__modelo_venda=DEFAULT_MODELO_VENDA,
    )
    return grupos_classe


def fechar_grupos_classe(company_id, grupos_classes_internal_ids):
    grupos_classe = get_grupos_classe(company_id, grupos_classes_internal_ids)
    vexado_grupos_classe_to_update = []
    for gc in grupos_classe:
        for vgc in gc.vexadogrupoclasse_set.all():
            if VexadoGrupoClasse.Status.CANCELADO not in vgc.status:
                vgc.status = VexadoGrupoClasse.Status.FECHADO
                vexado_grupos_classe_to_update.append(vgc)
    if vexado_grupos_classe_to_update:
        VexadoGrupoClasse.objects.bulk_update(vexado_grupos_classe_to_update, ["status"])  # não possui updated_at
    return {"grupos_fechados": len(vexado_grupos_classe_to_update)}


@shared_task(
    queue=DefaultQueueNames.CADASTROS_VEXADO,
    rate_limit=DefaultRateLimits.CADASTROS_VEXADO,
    ignore_result=True,
)
def cancela_passagens_e_inativar_trechos_task(passagens_ids, trechos_classe_rodoviaria_ids):
    passagens = list(Passagem.objects.filter(id__in=passagens_ids))
    cancela_passagens_pendentes_svc.cancelar_passagens(list(passagens))
    inativar_trechos_classe(trechos_classe_rodoviaria_ids)


def inativar_trechos_classe(trechos_classe_rodoviaria_ids):
    if not trechos_classe_rodoviaria_ids:
        return
    TrechoClasse.objects.filter(id__in=trechos_classe_rodoviaria_ids).update(active=False)


def _dispara_tasks(call_task_cancelar, cancelar_passagens_task, inativar_grupos_tasks):
    if call_task_cancelar and inativar_grupos_tasks:
        chain(cancelar_passagens_task, inativar_grupos_tasks).apply_async()
    elif call_task_cancelar and cancelar_passagens_task:
        cancelar_passagens_task.delay()


def inativar_grupos_classe_group_tasks(company_id, grupos_classe_external_ids):
    tasks = []
    for external_id in grupos_classe_external_ids:
        tasks.append(inativar_grupo_classe_task.si(company_id, external_id))
    return group(tasks)


@shared_task(
    queue=DefaultQueueNames.CADASTROS_VEXADO,
    rate_limit=DefaultRateLimits.CADASTROS_VEXADO,
    ignore_result=True,
)
def inativar_grupo_classe_task(company_id, grupo_classe_external_id):
    if not grupo_classe_external_id:  # TODO: não deixar ter grupo_classe_external_id como None no VexadoGrupoClasse
        return
    OrchestrateRodoviaria(company_id, modelo_venda=DEFAULT_MODELO_VENDA).inativar_grupo_classe(grupo_classe_external_id)
