from datetime import datetime, timedel<PERSON>

import time_machine
from django.db import connections
from model_bakery import baker
from zoneinfo import ZoneInfo

from commons.dateutils import timezone, to_default_tz
from rodoviaria.api.ti_sistemas import descobrir_operacao, models
from rodoviaria.models.core import LocalEmbarque, Rota, Rotina, RotinaTrechoVendido, TrechoVendido
from rodoviaria.tests.ti_sistemas.mock_data_response import mock_buscar_itinerario_normal, mock_trechos_vendidos


@time_machine.travel(datetime(2023, 1, 12, 16, 43, 34, tzinfo=ZoneInfo("America/Sao_Paulo")))
def test_descobrir_operacao(mocker, ti_sistemas_api, mock_buscar_lista_viagens):
    mock_group = mocker.patch("rodoviaria.api.ti_sistemas.descobrir_operacao.chain")
    task = descobrir_operacao.descobrir_operacao(
        ti_sistemas_api.login,
        next_days=10,
        shift_days=0,
    )

    mock_group.return_value.on_error.return_value.assert_called_once()
    assert task == mock_group.return_value.on_error.return_value
    assert (
        mock_group.call_args[0][0].tasks[0].task == "rodoviaria.api.ti_sistemas.descobrir_operacao._buscar_rota_servico"
    )


@time_machine.travel(datetime(2023, 1, 12, 16, 43, 34, tzinfo=ZoneInfo("America/Sao_Paulo")))
def test_descobrir_operacao_return_task_object(mocker, ti_sistemas_api, mock_buscar_lista_viagens):
    mock_group = mocker.patch("rodoviaria.api.ti_sistemas.descobrir_operacao.chain")
    task = descobrir_operacao.descobrir_operacao(
        ti_sistemas_api.login, next_days=10, shift_days=0, return_task_object=True
    )
    mock_group.return_value.on_error.return_value.assert_not_called()
    assert task == mock_group.return_value.on_error.return_value
    assert (
        mock_group.call_args[0][0].tasks[0].task == "rodoviaria.api.ti_sistemas.descobrir_operacao._buscar_rota_servico"
    )


@time_machine.travel(datetime(2023, 1, 12, 16, 43, 34, tzinfo=ZoneInfo("America/Sao_Paulo")))
def test_descobrir_operacao_inativa_rotas_e_rotinas_empresa(mocker, ti_sistemas_api, mock_buscar_lista_viagens):
    mocker.patch("rodoviaria.api.ti_sistemas.descobrir_operacao.group")
    rota = baker.make(Rota, company=ti_sistemas_api.company, ativo=True)
    rotina = baker.make(Rotina, rota=rota, datetime_ida=timezone.now() + timedelta(days=3), ativo=True)
    trecho_vendido = baker.make(TrechoVendido, rota=rota, ativo=True)
    descobrir_operacao.descobrir_operacao(ti_sistemas_api.login, next_days=10, shift_days=0)
    rota.refresh_from_db()
    assert rota.ativo is False
    rotina.refresh_from_db()
    assert rotina.ativo is False
    trecho_vendido.refresh_from_db()
    assert trecho_vendido.ativo is False


def test_buscar_rota_servico_cria_rota_rotina_trecho_vendido(
    django_assert_num_queries, ti_sistemas_api, mock_buscar_itinerario, mock_buscar_trechos_vendidos
):
    itinerario = models.Itinerario.parse_obj(mock_buscar_itinerario_normal.itinerario["list"])
    expected_hash = itinerario.hash
    expected_datetime_ida = to_default_tz(itinerario[0].datetime_ida)
    expected_trecho_vendido = mock_trechos_vendidos.trechos_vendidos["list"][0]
    with django_assert_num_queries(33, connection=connections["rodoviaria"]):
        descobrir_operacao._buscar_rota_servico(ti_sistemas_api.company.id, id_viagem=1212)
    rota = Rota.objects.get(company=ti_sistemas_api.company, id_hash=expected_hash)
    rotina = Rotina.objects.get(rota=rota, datetime_ida=expected_datetime_ida)
    trecho_vendido = TrechoVendido.objects.get(
        rota=rota,
        origem__id_external=expected_trecho_vendido["origemId"],
        destino__id_external=expected_trecho_vendido["destinoId"],
        classe=expected_trecho_vendido["classe"],
    )
    assert trecho_vendido.tipo_assento.tipo_assento_parceiro == expected_trecho_vendido["classe"]
    assert RotinaTrechoVendido.objects.get(rotina=rotina, trecho_vendido=trecho_vendido)


def test_buscar_rota_servico_cria_rota_rotina_trecho_vendido_atualiza_trechos_raw(
    mocker, ti_sistemas_api, mock_buscar_itinerario, mock_buscar_trechos_vendidos
):
    baker.make(
        LocalEmbarque,
        cidade__company=ti_sistemas_api.company,
        id_external=mock_trechos_vendidos.trechos_vendidos["list"][0]["origemId"],
        local_embarque_internal_id=1,
        nickname="Local 1",
        cidade__name="Cidade 1",
    )
    baker.make(
        LocalEmbarque,
        cidade__company=ti_sistemas_api.company,
        id_external=mock_trechos_vendidos.trechos_vendidos["list"][0]["destinoId"],
        local_embarque_internal_id=2,
        nickname="Local 2",
        cidade__name="Cidade 2",
    )
    mock_atualiza_trechos_raw = mocker.patch("rodoviaria.service.atualiza_operacao_utils.atualiza_trecho_batch")
    descobrir_operacao._buscar_rota_servico(ti_sistemas_api.company.id, id_viagem=1212)
    mock_atualiza_trechos_raw.delay.assert_called_once_with(
        [
            {
                "origem_internal_id": 1,
                "destino_internal_id": 2,
                "classe": "leito",
                "preco": 190.0,
                "datetime_ida": to_default_tz(datetime(2024, 2, 2, 14, 1)),
                "vagas": 12,
            }
        ],
        ti_sistemas_api.company.company_internal_id,
    )


def test_buscar_rota_servico_cria_rota_rotina_trecho_vendido_ignora_precos_absurdos(
    mocker, ti_sistemas_api, mock_buscar_itinerario, mock_buscar_trechos_vendidos_precos_absurdos
):
    mock_atualiza_trechos_raw = mocker.patch("rodoviaria.service.atualiza_operacao_utils.atualiza_trecho_batch")
    assert TrechoVendido.objects.filter(rota__company=ti_sistemas_api.company).count() == 0
    descobrir_operacao._buscar_rota_servico(ti_sistemas_api.company.id, id_viagem=1212)
    trechos_vendidos = list(TrechoVendido.objects.filter(rota__company=ti_sistemas_api.company))
    assert len(trechos_vendidos) == 2
    for tv in trechos_vendidos:
        assert tv.ativo is False
    mock_atualiza_trechos_raw.delay.assert_not_called()


def test_buscar_rota_servico_cria_checkpoints_e_locais_de_embarque(
    ti_sistemas_api, mock_buscar_itinerario, mock_buscar_trechos_vendidos
):
    itinerario = models.Itinerario.parse_obj(mock_buscar_itinerario_normal.itinerario["list"])
    expected_hash = itinerario.hash
    expected_checkpoints_len = len(itinerario)
    expected_first_local_embarque = itinerario[0]
    assert not LocalEmbarque.objects.filter(cidade__company=ti_sistemas_api.company).exists()
    descobrir_operacao._buscar_rota_servico(ti_sistemas_api.company.id, id_viagem=1212)
    rota = Rota.objects.get(company=ti_sistemas_api.company, id_hash=expected_hash)
    assert rota.itinerario.count() == expected_checkpoints_len
    assert LocalEmbarque.objects.get(
        cidade__company=ti_sistemas_api.company, id_external=expected_first_local_embarque.local.external_local_id
    )


def test_buscar_rota_servico_atualiza_rota_rotina(
    django_assert_num_queries, ti_sistemas_api, mock_buscar_itinerario, mock_buscar_trechos_vendidos
):
    itinerario = models.Itinerario.parse_obj(mock_buscar_itinerario_normal.itinerario["list"])
    locais = {}
    for cp in itinerario:
        locais[cp.local.external_local_id] = baker.make(
            LocalEmbarque, id_external=cp.local.external_local_id, cidade__company=ti_sistemas_api.company
        )
    rota = baker.make(Rota, company=ti_sistemas_api.company, id_hash=itinerario.hash, ativo=False)
    rotina = baker.make(Rotina, rota=rota, datetime_ida=to_default_tz(itinerario[0].datetime_ida), ativo=False)
    expected_trecho_vendido = mock_trechos_vendidos.trechos_vendidos["list"][0]
    trecho_vendido = baker.make(
        TrechoVendido,
        rota=rota,
        origem__id_external=expected_trecho_vendido["origemId"],
        destino__id_external=expected_trecho_vendido["destinoId"],
        classe=expected_trecho_vendido["classe"],
        capacidade_classe=expected_trecho_vendido["capacidade"],
        ativo=False,
        origem=locais[str(expected_trecho_vendido["origemId"])],
        destino=locais[str(expected_trecho_vendido["destinoId"])],
    )
    with django_assert_num_queries(23, connection=connections["rodoviaria"]):
        descobrir_operacao._buscar_rota_servico(ti_sistemas_api.company.id, id_viagem=1212)
    rota.refresh_from_db()
    assert rota.ativo is True
    rotina.refresh_from_db()
    assert rotina.ativo is True
    trecho_vendido.refresh_from_db()
    assert trecho_vendido.ativo is True
    assert trecho_vendido.tipo_assento.tipo_assento_parceiro == expected_trecho_vendido["classe"]
    assert RotinaTrechoVendido.objects.get(rotina=rotina, trecho_vendido=trecho_vendido)
