from datetime import datetime, <PERSON><PERSON><PERSON>
from decimal import Decimal as D
from types import SimpleNamespace
from unittest import mock

import pytest
import time_machine
from model_bakery import baker
from zoneinfo import ZoneInfo

from commons.dateutils import timezone, to_default_tz_required, today_midnight
from rodoviaria.api.rodoviaria_api import RodoviariaAPI
from rodoviaria.models.core import Company, Passagem, Remanejamento
from rodoviaria.service.exceptions import DivergenciaPrecoException


def test_not_implemented_errors(totalbus_company):
    rodoviaria_api = RodoviariaAPI(totalbus_company)
    with pytest.raises(NotImplementedError, match=r"Método _make_request não implementado"):
        rodoviaria_api._make_request()
    with pytest.raises(NotImplementedError, match=r"Método _check_response não implementado"):
        rodoviaria_api._check_response()
    with pytest.raises(NotImplementedError, match=r"Método atualiza_origens não implementado"):
        rodoviaria_api.atualiza_origens()
    with pytest.raises(NotImplementedError, match=r"Método cancela_venda não implementado"):
        rodoviaria_api.cancela_venda({})
    with pytest.raises(NotImplementedError, match=r"Método comprar não implementado"):
        rodoviaria_api.comprar({})
    with pytest.raises(NotImplementedError, match=r"Método buscar_itinerario não implementado"):
        rodoviaria_api.buscar_itinerario({})
    with pytest.raises(NotImplementedError, match=r"Método lista_cidades não implementado"):
        rodoviaria_api.lista_cidades()
    with pytest.raises(NotImplementedError, match=r"Método dados_bpe_passagem não implementado"):
        rodoviaria_api.dados_bpe_passagem({})
    with pytest.raises(NotImplementedError, match=r"Método buscar_todos_servicos não implementado"):
        rodoviaria_api.buscar_todos_servicos()
    with pytest.raises(
        NotImplementedError,
        match=r"Método add_multiple_pax_na_lista_passageiros_viagem não implementado",
    ):
        rodoviaria_api.add_multiple_pax_na_lista_passageiros_viagem({})
    with pytest.raises(NotImplementedError, match=r"Método get_mapas_veiculos_api não implementado"):
        rodoviaria_api.get_mapas_veiculos_api()
    with pytest.raises(NotImplementedError, match=r"Método get_veiculos_api não implementado"):
        rodoviaria_api.get_veiculos_api()
    with pytest.raises(NotImplementedError, match=r"Método create_veiculos_api não implementado"):
        rodoviaria_api.create_veiculos_api({})
    with pytest.raises(NotImplementedError, match=r"Método altera_veiculo_viagem não implementado"):
        rodoviaria_api.altera_veiculo_viagem({}, 1, {})
    with pytest.raises(NotImplementedError, match=r"Método viagens_por_rota não implementado"):
        rodoviaria_api.viagens_por_rota(1)
    with pytest.raises(NotImplementedError, match=r"Método lista_reservas_viagem não implementado"):
        rodoviaria_api.lista_reservas_viagem(1)
    with pytest.raises(
        NotImplementedError,
        match=r"Método cancelar_reserva_por_localizador não implementado",
    ):
        rodoviaria_api.cancelar_reserva_por_localizador(1)
    with pytest.raises(NotImplementedError, match=r"Método desbloquear_poltronas não implementado"):
        rodoviaria_api.desbloquear_poltronas(123, [12, 15])
    with pytest.raises(
        NotImplementedError,
        match=f"Método inativar_grupo_classe não implementado para API {totalbus_company.integracao.name}",
    ):
        rodoviaria_api.inativar_grupo_classe(1)
    with pytest.raises(NotImplementedError, match="Método buscar_servicos_por_data não implementado"):
        rodoviaria_api.buscar_servicos_por_data(None, None)
    with pytest.raises(
        NotImplementedError,
        match=f"Método cancelar_reservas_por_pedido_id não implementado para API {totalbus_company.integracao.name}",
    ):
        rodoviaria_api.cancelar_reservas_por_pedido_id(1)


def test_rg_or_cpf(totalbus_company):
    rodoviaria_api = RodoviariaAPI(totalbus_company)
    assert rodoviaria_api.rg_or_cpf(SimpleNamespace(cpf="111.111.111-11", rg_number="123.123.213")) == "111.111.111-11"
    assert rodoviaria_api.rg_or_cpf(SimpleNamespace(rg_number="123.123.213")) == "123.123.213"


def test_does_class_mactch(totalbus_company):
    rodoviaria_api = RodoviariaAPI(totalbus_company)
    assert rodoviaria_api._does_class_match("semi leito", "semi-leito")
    assert rodoviaria_api._does_class_match("cama premium", "cama diamante") is False

    # TODO: gambiarra para adamantina
    totalbus_company.name = "Expresso Adamantina"
    totalbus_company.company_internal_id = 282
    totalbus_company.save()
    assert rodoviaria_api._does_class_match("semi leito", "convencional")
    assert rodoviaria_api._does_class_match("leito", "convencional") is False


def test_get_passagens_confirmadas(totalbus_company):
    rodoviaria_api = RodoviariaAPI(totalbus_company)
    trecho_classe_1 = baker.make("rodoviaria.TrechoClasse", trechoclasse_internal_id=44)
    trecho_classe_2 = baker.make("rodoviaria.TrechoClasse", trechoclasse_internal_id=45)
    baker.make(
        "rodoviaria.Passagem",
        travel_internal_id=10,
        status="confirmada",
        trechoclasse_integracao=trecho_classe_1,
        buseiro_internal_id=23,
    )
    baker.make(
        "rodoviaria.Passagem",
        travel_internal_id=10,
        status="confirmada",
        trechoclasse_integracao=trecho_classe_2,
        buseiro_internal_id=35,
    )
    baker.make(
        "rodoviaria.Passagem",
        travel_internal_id=10,
        status="confirmada",
        trechoclasse_integracao=trecho_classe_2,
        buseiro_internal_id=36,
    )
    assert len(rodoviaria_api.get_passagens_confirmadas(20)) == 0
    assert len(rodoviaria_api.get_passagens_confirmadas(10)) == 3
    assert len(rodoviaria_api.get_passagens_confirmadas(10, buseiro_id=36)) == 1
    assert len(rodoviaria_api.get_passagens_confirmadas(10, buseiro_id=36)) == 1


def test_rota_id_from_trecho_classe(totalbus_company):
    rodoviaria_api = RodoviariaAPI(totalbus_company)
    assert rodoviaria_api.rota_id_from_trecho_classe({}) is None


def test_fetch_data_limite_servicos(praxio_login):
    retorno_mock = {
        12: datetime(2022, 1, 1, 1, 1),
        13: datetime(2022, 1, 1, 1, 1),
    }
    retorno_mock_segunda_rodada = {
        12: datetime(2022, 2, 1, 1, 1),
        13: datetime(2022, 1, 1, 1, 1),
    }

    with mock.patch.object(RodoviariaAPI, "buscar_servicos_por_data") as mock_buscar_servicos:
        mock_buscar_servicos.side_effect = [
            retorno_mock,
            retorno_mock_segunda_rodada,
            None,
        ]
        rodoviaria_api = RodoviariaAPI(praxio_login)
        result = rodoviaria_api.fetch_data_limite_servicos(today_midnight())

    assert len(result) == 2
    assert result[12] == datetime(2022, 2, 1, 1, 1)
    assert result[13] == datetime(2022, 1, 1, 1, 1)


def test_add_pax_na_lista_passageiros_viagem_com_passagem_ja_emitida(guiche_company):
    travel_id = 83123
    trecho_classe_id = 123
    buseiro_id = 8312
    params = SimpleNamespace(
        trechoclasse_id=trecho_classe_id, passenger=SimpleNamespace(buseiro_id=buseiro_id), travel_id=travel_id
    )
    baker.make(
        Passagem, travel_internal_id=travel_id, buseiro_internal_id=buseiro_id, status=Passagem.Status.CONFIRMADA
    )
    response = RodoviariaAPI(guiche_company).add_pax_na_lista_passageiros_viagem(params)
    assert response == {"sucesso": True, "already_on_api": True}


@time_machine.travel(datetime(2022, 7, 20, 16, 43, 34, tzinfo=ZoneInfo("America/Sao_Paulo")))
def test_add_pax_na_lista_passageiros_viagem_com_remanejamento_pendente(guiche_company):
    travel_id = 83123
    trecho_classe_id = 123
    buseiro_id = 8312
    params = SimpleNamespace(
        trechoclasse_id=trecho_classe_id, passenger=SimpleNamespace(buseiro_id=buseiro_id), travel_id=travel_id
    )
    rem = baker.make(Remanejamento, travel_destino_internal_id=travel_id, status=Remanejamento.Status.PENDENTE)
    rem.created_at = timezone.now() - timedelta(minutes=10)
    response = RodoviariaAPI(guiche_company).add_pax_na_lista_passageiros_viagem(params)
    assert response == {"sucesso": False, "warning": "Remanejamento será executado assíncronamente"}


def test_queue_name(mocker, guiche_company):
    mocker.patch("commons.celery_utils.is_deployed_queue", return_value=True)
    guiche_company.name = "Formiguinha Atômica - 22"
    assert RodoviariaAPI(guiche_company).queue_name == "bp_formiguinha_atomica_22"


def test_queue_name_hibrido(mocker, guiche_company):
    mocker.patch("commons.celery_utils.is_deployed_queue", return_value=True)
    guiche_company.name = "Formiguinha Atômica - 23"
    guiche_company.modelo_venda = Company.ModeloVenda.HIBRIDO
    assert RodoviariaAPI(guiche_company).queue_name == "bp_formiguinha_atomica_23_hibrido"


def test_datetime_ida_from_trechoclasse(eulabs_company):
    rodoviaria_api = RodoviariaAPI(eulabs_company)
    grupo = baker.make("rodoviaria.Grupo")
    trecho_classe = baker.make("rodoviaria.TrechoClasse", grupo=grupo)
    assert rodoviaria_api.datetime_ida_from_trecho_classe(trecho_classe) == grupo.datetime_ida


def test_create_passagens__error(eulabs_company):
    rodoviaria_api = RodoviariaAPI(eulabs_company)
    preco_rodoviaria = D("100")
    valor_cheio = preco_rodoviaria - D("0.51") * preco_rodoviaria

    trecho_classe = baker.make("rodoviaria.TrechoClasse")
    passagens = [
        Passagem(
            trechoclasse_integracao_id=trecho_classe.id,
            company_integracao_id=eulabs_company.id,
            buseiro_internal_id=4231,
            poltrona_external_id=2,
            travel_internal_id=8888,
            localizador="teste",
            pedido_external_id="teste",
            status=Passagem.Status.CONFIRMADA,
            valor_cheio=valor_cheio,
            bpe_em_contingencia=False,
            created_at="2024-01-02T10:47:12-03:00",
            updated_at="2024-01-02T10:48:12",
            erro=None,
            erro_cancelamento=None,
            datetime_cancelamento=None,
            numero_passagem="64534234",
            preco_rodoviaria=preco_rodoviaria,
        )
    ]

    with pytest.raises(DivergenciaPrecoException):
        passagens = rodoviaria_api.create_passagens(passagens)

    assert all([passagem.status == Passagem.Status.CANCELADA] for passagem in passagens)


def test_create_passagens(eulabs_company):
    rodoviaria_api = RodoviariaAPI(eulabs_company)
    preco_rodoviaria = D("100")
    valor_cheio = preco_rodoviaria - D("0.5") * preco_rodoviaria

    trecho_classe = baker.make("rodoviaria.TrechoClasse")
    passagens = [
        Passagem(
            trechoclasse_integracao_id=trecho_classe.id,
            company_integracao_id=eulabs_company.id,
            buseiro_internal_id=4231,
            poltrona_external_id=2,
            travel_internal_id=8888,
            localizador="teste",
            pedido_external_id="teste",
            status=Passagem.Status.CONFIRMADA,
            valor_cheio=valor_cheio,
            bpe_em_contingencia=False,
            created_at="2024-01-02T10:47:12-03:00",
            updated_at="2024-01-02T10:48:12",
            erro=None,
            erro_cancelamento=None,
            datetime_cancelamento=None,
            numero_passagem="64534234",
            preco_rodoviaria=preco_rodoviaria,
        )
    ]

    passagens = rodoviaria_api.create_passagens(passagens)

    assert all([passagem.status == Passagem.Status.CONFIRMADA] for passagem in passagens)


@pytest.mark.parametrize(
    "datetime_diff,modelo_venda,features,result",
    [
        (0, Company.ModeloVenda.HIBRIDO, [], True),
        (2, Company.ModeloVenda.HIBRIDO, [], False),
        (0, Company.ModeloVenda.MARKETPLACE, [], True),
        (15, Company.ModeloVenda.MARKETPLACE, [], True),
        (35, Company.ModeloVenda.MARKETPLACE, [], False),
        (-1, Company.ModeloVenda.MARKETPLACE, [], True),
        (0, Company.ModeloVenda.MARKETPLACE, [Company.Feature.ZERO_TOLERANCIA_MATCH_HORARIO], True),
        (1, Company.ModeloVenda.MARKETPLACE, [Company.Feature.ZERO_TOLERANCIA_MATCH_HORARIO], False),
    ],
)
def test_match_datetime_ida_servico(guiche_company, datetime_diff, modelo_venda, features, result):
    guiche_company.features = features
    guiche_company.modelo_venda = modelo_venda
    datetime_ida = to_default_tz_required(datetime(2022, 2, 2, 18, 40))
    datetime_ida_servico = datetime_ida + timedelta(minutes=datetime_diff)
    timezone = "America/Sao_Paulo"
    api = RodoviariaAPI(guiche_company)
    assert api._match_datetime_ida_servico(datetime_ida, timezone, datetime_ida_servico) == result
