import json

from rodoviaria import views
from rodoviaria.api.guichepass import models as guichepass_models
from rodoviaria.api.guichepass.memcache import GuichepassMC
from rodoviaria.models.core import Passagem
from rodoviaria.tests.middleware import request_with_middleware
from rodoviaria.tests.totalbus import mocker as mocker_totalbus


def test_guiche_reservar_e_cancelar_viagem(
    rf,
    mocker,
    guiche_trechoclasses,
    mock_guiche_comprar,
    mock_guiche_cancelar,
    guiche_login,
    mock_dispara_atualizacao_trecho,
):
    mocker.patch.object(
        GuichepassMC,
        "get_poltrona_bloqueada_cache",
        return_value=guichepass_models.InfosCacheaveisBloqueioPoltrona(
            reserva_id=1234, preco=guiche_trechoclasses.ida.preco_rodoviaria
        ),
    )
    _compra_remove_cancela(rf, guiche_trechoclasses.ida.trechoclasse_internal_id, False)


def test_guiche_erro_confirmar_compra(rf, mocker, guiche_trechoclasses, guiche_mock_login, guiche_login):
    mocker.patch.object(
        GuichepassMC,
        "get_poltrona_bloqueada_cache",
        return_value=guichepass_models.InfosCacheaveisBloqueioPoltrona(
            reserva_id=1234, preco=guiche_trechoclasses.ida.preco_rodoviaria
        ),
    )
    params = _compra_params(guiche_trechoclasses.ida.trechoclasse_internal_id)

    _comprar_com_erro(rf, params, expected_status_code=504)


def test_praxio_reservar_e_cancelar_viagem(
    rf, praxio_trechoclasses, mock_praxio_comprar, mock_praxio_cancelar, praxio_login, mock_dispara_atualizacao_trecho
):
    _compra_remove_cancela(rf, praxio_trechoclasses.ida.trechoclasse_internal_id)


def test_praxio_erro_confirmar_compra(rf, praxio_trechoclasses, mock_praxio_erro_confirmar_venda, praxio_login):
    params = _compra_params(praxio_trechoclasses.ida.trechoclasse_internal_id)
    _comprar_com_erro(rf, params, expected_status_code=504)


def test_ti_sistemas_reservar_e_cancelar_viagem(
    rf,
    ti_sistemas_trechoclasses,
    mock_ti_sistemas_comprar,
    mock_ti_sistemas_cancelar,
    ti_sistemas_login,
    mock_dispara_atualizacao_trecho,
):
    _compra_remove_cancela(rf, ti_sistemas_trechoclasses.ida.trechoclasse_internal_id)


def test_totalbus_reservar_e_cancelar_viagem(
    rf,
    totalbus_trechoclasses,
    mock_totalbus_comprar,
    mock_totalbus_cancelar,
    totalbus_login,
    mock_dispara_atualizacao_trecho,
):
    preco = mocker_totalbus.BloquearPoltrona.response()["preco"]["preco"]
    _compra_remove_cancela(rf, totalbus_trechoclasses.ida.trechoclasse_internal_id, valor_cheio=preco)


def test_totalbus_erro_confirmar_compra(rf, totalbus_trechoclasses, mock_totalbus_erro_confirmar_venda, totalbus_login):
    preco = mocker_totalbus.BloquearPoltrona.response()["preco"]["preco"]
    params = _compra_params(totalbus_trechoclasses.ida.trechoclasse_internal_id, valor_cheio=preco)
    _comprar_com_erro(
        rf,
        params,
        expected_status_code=504,
    )


def test_totalbus_erro_venda_impedida(rf, totalbus_trechoclasses, mock_totalbus_erro_venda_impedida, totalbus_login):
    params = _compra_params(totalbus_trechoclasses.ida.trechoclasse_internal_id)
    data = _comprar_com_erro(
        rf,
        params,
        expected_status_code=410,
        expected_passagens=0,
    )
    assert data["error_type"] == "service_not_for_sale"


def _compra_params(trechoclasse_id, valor_cheio=305.45):
    return {
        "trechoclasse_id": trechoclasse_id,
        "travel_id": 217,
        "poltronas": [9, 10],
        "valor_cheio": valor_cheio,
        "passageiros": [
            {
                "id": 31,
                "name": "Sophie Peixoto",
                "cpf": "62287847065",
                "rg_number": "*********",
                "rg_orgao": None,
                "tipo_documento": "RG",
                "phone": "52975156354",
            },
            {
                "id": 32,
                "name": "Francisco Silva",
                "cpf": "60383705010",
                "rg_number": "*********",
                "rg_orgao": None,
                "tipo_documento": "RG",
                "phone": "34999931781",
            },
        ],
    }


def _compra_remove_cancela(rf, trechoclasse_id, remover_implemented=True, valor_cheio=305.42):
    params = _compra_params(trechoclasse_id, valor_cheio=valor_cheio)
    request = rf.post(
        "rodoviaria/compra/comprar",
        data=json.dumps(params),
        content_type="application/json",
    )
    response = views.efetua_compra(request)
    data = json.loads(response.content)
    passagens = []
    assert len(data["passagens"]) == 2
    for passagem in data["passagens"]:
        assert passagem["buseiro_id"] in [31, 32]
        assert passagem["travel_id"] == 217
        passagem_obj = Passagem.objects.get(id=passagem["id"])
        assert passagem_obj.status == Passagem.Status.CONFIRMADA
        assert passagem_obj.preco_rodoviaria is not None
        passagens.append(passagem_obj)

    if remover_implemented:
        # remover passageiro
        request = rf.get("/rodoviaria/compra/cancela")
        response = views.efetua_cancelamento(
            request,
            travel_id=passagem_obj.travel_internal_id,
            buseiro_id=passagem_obj.buseiro_internal_id,
        )

        passagens[0].refresh_from_db()
        passagens[1].refresh_from_db()
        assert passagens[0].status == Passagem.Status.CONFIRMADA
        assert passagens[1].status == Passagem.Status.CANCELADA

    # cancelar
    params = {"travel_id": 217}
    request = rf.post("rodoviaria/compra/comprar", params=params)
    response = views.efetua_cancelamento(request, **params)

    passagens[0].refresh_from_db()
    passagens[1].refresh_from_db()
    assert passagens[0].status == Passagem.Status.CANCELADA
    assert passagens[1].status == Passagem.Status.CANCELADA


def _comprar_com_erro(rf, params, expected_status_code=200, expected_passagens=2):
    request = rf.post(
        "/rodoviaria/v1/compra/comprar",
        data=json.dumps(params),
        content_type="application/json",
    )

    response = request_with_middleware(request)

    assert response.status_code == expected_status_code
    data = json.loads(response.content)
    assert "error" in data
    passagens = Passagem.objects.filter(travel_internal_id=217).order_by("id").values("status")
    assert len(passagens) == expected_passagens
    for passagem in passagens:
        assert passagem["status"] in [Passagem.Status.INCOMPLETA, Passagem.Status.ERRO]
    return data
