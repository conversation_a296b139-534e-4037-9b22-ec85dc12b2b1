"""
Django settings for bp project.

Generated by 'django-admin startproject' using Django 3.2.5.

For more information on this file, see
https://docs.djangoproject.com/en/3.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/3.2/ref/settings/
"""

from collections import OrderedDict
from pathlib import Path

import sentry_sdk
from decouple import config
from decouple_aws import get_config
from sentry_deduplicate_integration import SentryDeduplicateIntegration
from sentry_sdk.integrations.django import DjangoIntegration

from commons import redis
from commons.circuit_breaker import MyCircuitBreakerError

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

ENVIRONMENT = config("RODOVIARIA_ENVIRONMENT", default="local")

AWS_SECRETS_MANAGER_REGIONS = {
    "test": "us-east-2",
    "prod": "sa-east-1",
}

if ENVIRONMENT in AWS_SECRETS_MANAGER_REGIONS.keys():
    config = get_config(f"rodoviaria/{ENVIRONMENT}", AWS_SECRETS_MANAGER_REGIONS[ENVIRONMENT])

REDIS_URL = config("REDIS_URL", default=None)

CELERY_BROKER_URL = config("CELERY_BROKER_URL", default=None)
BUSERDJANGO_CELERY_BROKER_URL = config("BUSERDJANGO_CELERY_BROKER_URL", default=None)

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/3.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = config("DJANGO_SECRET_KEY", default="a_secret_key")

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = config("DJANGO_DEBUG", default=True, cast=bool)
DEBUG_SQL = config("DEBUG_SQL", default=False, cast=bool)

DATA_UPLOAD_MAX_MEMORY_SIZE = 10_000_000

SENTRY_DSN = config("RODOVIARIA_SENTRY_DSN", default=None)

if SENTRY_DSN is not None:
    sentry_sdk.init(
        dsn=SENTRY_DSN,
        integrations=[
            DjangoIntegration(),
            SentryDeduplicateIntegration(redis_factory=redis.get_master_client, max_events_per_minute=5),
        ],
        traces_sample_rate=0.01,
        send_default_pii=True,
        environment=ENVIRONMENT,
        ignore_errors=[MyCircuitBreakerError],
    )

HONEYCOMB_API_KEY = config("RODOVIARIA_HONEYCOMB_API_KEY", default=None)
HONEYCOMB_DATASET = config("RODOVIARIA_HONEYCOMB_DATASET", default=None)
HONEYCOMB_SERVICE_NAME = config("RODOVIARIA_HONEYCOMB_SERVICE_NAME", default="rodoviaria")
HONEYCOMB_SAMPLE_RATE = config("RODOVIARIA_HONEYCOMB_SAMPLE_RATE", default=1, cast=int)

ALLOWED_HOSTS = ["*"]


# Application definition

INSTALLED_APPS = [
    "constance",
    "core",
    "rodoviaria",
    "marketplace",
    "taggit",
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    "django_extensions",
    "django_linear_migrations",
    "django_migrations_ci",
    "memoize",
    "cid",
]

CONSTANCE_SUPERUSER_ONLY = False
CONSTANCE_IGNORE_ADMIN_VERSION_CHECK = True

CONSTANCE_BUCKETS = {
    "BUCKET_SIZE_PRAXIO": (15, "set bucket_size_praxio", int),
    "REFRESH_INTERVAL_PRAXIO": (6, "set refresh_interval_praxio", int),
    "BUCKET_SIZE_TOTALBUS": (16, "set bucket_size_totalbus", int),
    "REFRESH_INTERVAL_TOTALBUS": (6, "set refresh_interval_totalbus", int),
    "BUCKET_SIZE_ADAMANTINA": (12, "set bucket_size_totalbus", int),
    "REFRESH_INTERVAL_ADAMANTINA": (15, "set refresh_interval_totalbus", int),
    "BUCKET_SIZE_EXP_NORDESTE": (12, "set bucket_size_totalbus", int),
    "REFRESH_INTERVAL_EXP_NORDESTE": (15, "set refresh_interval_totalbus", int),
    "BUCKET_SIZE_GRUPO_JUINA": (5, "set bucket_size_grupo_juina", int),
    "REFRESH_INTERVAL_GRUPO_JUINA": (30, "set refresh_interval_grupo_juina", int),
    "BUCKET_SIZE_EULABS": (10, "set bucket_size_eulabs", int),
    "REFRESH_INTERVAL_EULABS": (10, "set refresh_interval_eulabs", int),
    "BUCKET_SIZE_VEXADO": (120, "set bucket_size_vexado", int),
    "REFRESH_INTERVAL_VEXADO": (60, "set refresh_interval_vexado", int),
    "BUCKET_SIZE_GUICHE": (70, "set bucket_size_guiche", int),
    "REFRESH_INTERVAL_GUICHE": (60, "set refresh_interval_guiche", int),
    "BUCKET_SIZE_SMARTBUS": (70, "set bucket_size_smartbus", int),
    "REFRESH_INTERVAL_SMARTBUS": (60, "set refresh_interval_smartbus", int),
    "BUCKET_SIZE_TI_SISTEMAS": (70, "set bucket_size_ti_sistemas", int),
    "REFRESH_INTERVAL_TI_SISTEMAS": (60, "set refresh_interval_ti_sistemas", int),
}

CONSTANCE_TIMEOUTS = {
    "TOTALBUS_TIMEOUT_CONFIRMAR_VENDA": (180, "timeout do endpoint confirmar venda da totalbus", int),
    "TOTALBUS_TIMEOUT_BUSCAR_SERVICOS": (180, "timeout do endpoint integracao/padrao/buscarServicos", int),
    "TOTALBUS_TIMEOUT_BUSCAR_TODOS_SERVICOS": (120, "timeout do endpoint buscar todos servicos disponiveis"),
    "TOTALBUS_TIMEOUT_DEFAULT": (60, "timeout padrão totalbus", int),
}

TEMP_CONFIGS = {
    "DESBLOQUEIA_POLTRONA_NO_CANCELAMENTO_PRAXIO": (
        True,
        "Teste para remover desbloqueio de poltronas no cancelamento da Praxio",
        bool,
    ),
    "USE_HTTP_ASYNC": (
        False,
        "Usa executor async para requests HTTP",
        bool,
    ),
    "ENABLE_PROMETHEUS": (
        False,
        "Habilita coleta de métricas.",
        bool,
    ),
    "ROTAS_PARA_REINTEGRAR": ("", "Força a reintegração das rotas listadas", str),
    "EMPRESAS_COM_EMISSAO_CONEXAO_SYNC": ("", "Emite sincronamente as passagens de conexão das empresas listadas", str),
    "FORCE_RENEW_LINK_ON_ADD_PAX": (True, "Força refazer o link do trecho_classe no add_pax", bool),
    "TRAVELS_PERMITE_EMISSAO_MENOS_DE_3_HORAS": (
        "",
        "Permite emissão de passagens com menos de 3 horas de antecedência",
        str,
    ),
}


ROTINAS_INTEGRADAS = {
    "ROTINAS_INTEGRADAS_RODEROTAS": ("", "rotinas integradas pela RodeRotas híbrido", str),
    "SERVICOS_COMPRA_POLTRONAS_ADAMANTINA": ("", "servicos_compra_poltronas_Adamantina", str),
    "ROTAS_FIXAS_HIBRIDO": ("", "Rotas integradas manualmente no modelo híbrido", str),
}

CONFIGS_EMPRESAS = {
    "PRAXIO_TIPO_PAGAMENTO_POR_EMPRESA": ("{}", "Tipo de pagamento configurado por empresa para a OTA Praxio", str),
}


CONSTANCE_CONFIG = {
    **CONSTANCE_BUCKETS,
    **CONSTANCE_TIMEOUTS,
    **ROTINAS_INTEGRADAS,
    **TEMP_CONFIGS,
    **CONFIGS_EMPRESAS,
}

CONSTANCE_CONFIG_FIELDSETS = OrderedDict(
    [
        ("Token_bucket options", {"fields": (CONSTANCE_BUCKETS.keys()), "collapse": True}),
        ("Timeout options", {"fields": (CONSTANCE_TIMEOUTS.keys()), "collapse": True}),
        ("Rotinas Integradas", {"fields": (ROTINAS_INTEGRADAS.keys()), "collapse": True}),
        ("Configuracoes temporarias", {"fields": (TEMP_CONFIGS.keys()), "collapse": True}),
        ("Configuracoes empresas", {"fields": (CONFIGS_EMPRESAS.keys()), "collapse": True}),
    ]
)

CONSTANCE_BACKEND = config("CONSTANCE_BACKEND", default="constance.backends.memory.MemoryBackend")
CONSTANCE_REDIS_CACHE_TIMEOUT = 60
CONSTANCE_REDIS_CONNECTION = REDIS_URL

MIDDLEWARE = [
    "cid.middleware.CidMiddleware",
    "django.middleware.security.SecurityMiddleware",
    "whitenoise.middleware.WhiteNoiseMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "request_context.middleware.request_context_middleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
    "beeline.middleware.django.HoneyMiddleware",
    "commons.middleware.request_log_middleware",
    "commons.middleware.sentry_middleware",
    "commons.middleware.honeycomb_middleware",
    "django_query_prefixer.middlewares.request_route",
    "commons.middleware.error_middleware",
]

ROOT_URLCONF = "bp.urls"

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

WSGI_APPLICATION = "bp.wsgi.application"


# Database
# https://docs.djangoproject.com/en/3.2/ref/settings/#databases

DATABASES = {
    "default": {
        "ENGINE": "django_query_prefixer.backends.postgresql",
        "NAME": config("DB_NAME", default="buser"),
        "USER": config("DB_USER", default="buser"),
        "PASSWORD": config("DB_PASS", default="buser"),
        "HOST": config("DB_HOST", default="localhost"),
        "PORT": config("DB_PORT", default="5432"),
        "CONN_MAX_AGE": config("DB_CONN_MAX_AGE", default=0, cast=int),
        "DISABLE_SERVER_SIDE_CURSORS": True,
        "TEST": {"NAME": "test_buser_django"},
        "OPTIONS": {
            "application_name": config("APPLICATION_NAME", "rodoviaria"),
            "options": "-c statement_timeout=600000 -c idle_in_transaction_session_timeout=600000",
        },
    },
    "rodoviaria": {
        "ENGINE": "django_query_prefixer.backends.postgresql",
        "NAME": config("RODOVIARIA_DB_NAME", default="buser_rodoviaria"),
        "USER": config("RODOVIARIA_DB_USER", default="buser"),
        "PASSWORD": config("RODOVIARIA_DB_PASS", default="buser"),
        "HOST": config("RODOVIARIA_DB_HOST", default="localhost"),
        "PORT": config("RODOVIARIA_DB_PORT", default="5432"),
        "CONN_MAX_AGE": config("RODOVIARIA_DB_CONN_MAX_AGE", default=0, cast=int),
        "OPTIONS": {
            "application_name": config("APPLICATION_NAME", "rodoviaria"),
        },
    },
}

if pgbouncer_host := config("PGBOUNCER_HOST", default=None):
    DATABASES["rodoviaria"].update(
        {
            "HOST": pgbouncer_host,
            "PORT": config("PGBOUNCER_PORT", default=6432, cast=int),
        }
    )


DATABASE_ROUTERS = ["rodoviaria.rodoviaria_router.RodoviariaRouter"]

if config("ENABLE_PGLOGICAL_MIGRATIONS", default=False, cast=bool):
    DATABASES["rodoviaria"]["ENGINE"] = "bp.db.pglogical"

# Password validation
# https://docs.djangoproject.com/en/3.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]


# Internationalization
# https://docs.djangoproject.com/en/3.2/topics/i18n/

LANGUAGE_CODE = "pt-br"

TIME_ZONE = "America/Sao_Paulo"

USE_I18N = True

USE_L10N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/3.2/howto/static-files/

STATIC_URL = "/static/"

STATIC_ROOT = BASE_DIR / "static"

SECURE_PROXY_SSL_HEADER = ("HTTP_X_FORWARDED_PROTO", "https")

# Default primary key field type
# https://docs.djangoproject.com/en/3.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"


# Cache
# https://docs.djangoproject.com/en/3.2/topics/cache/

MEMCACHED_HOST = config("MEMCACHED_HOST", default=None)

CACHES = {
    "default": {
        "BACKEND": "commons.memcache.MockcacheCache",
        "LOCATION": "rodoviariadefault",
    },
    "pymemcache": {
        "BACKEND": "commons.memcache.MockcacheCache",
        "LOCATION": "rodoviaria_pymemcache",
    },
    "redis": {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": "redis://localhost:6379",
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
            "REDIS_CLIENT_CLASS": "redislite.Redis",
            "FAKE": True,
        },
    },
}

DJANGO_REDIS_CONNECTION_FACTORY = "commons.redis.ConnectionFactory"

if MEMCACHED_HOST:
    CACHES["default"] = {
        "BACKEND": "django.core.cache.backends.memcached.PyMemcacheCache",
        "LOCATION": MEMCACHED_HOST,
        "KEY_PREFIX": "rodoviaria_pymemcache",
    }
    CACHES["pymemcache"] = {
        "BACKEND": "django.core.cache.backends.memcached.PyMemcacheCache",
        "LOCATION": MEMCACHED_HOST,
        "KEY_PREFIX": "rodoviaria_pymem",
    }

if REDIS_URL:
    CACHES["redis"] = {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": REDIS_URL.split(","),
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
            "SOCKET_CONNECT_TIMEOUT": 10,  # seconds
            "SOCKET_TIMEOUT": 30,  # seconds
            "CONNECTION_POOL_KWARGS": {
                "retry_on_timeout": True,  # default retry: once, no backoff
            },
        },
    }


# Logging
# https://docs.djangoproject.com/en/3.2/topics/logging/

LOGSTASH_HOST = config("LOGSTASH_HOST", default=None)
LOGSTASH_PORT = config("LOGSTASH_PORT", default=0, cast=int)

LOGGING = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "simple": {"format": "%(levelname)s [%(asctime)s] %(message)s"},
    },
    "filters": {
        "correlation": {"()": "bp.logging.CorrelationIdContextFilter"},
        "current_integration": {"()": "bp.logging.CurrentIntegrationContextFilter"},
    },
    "handlers": {
        "console": {
            "level": "DEBUG" if DEBUG else "INFO",
            "class": "logging.StreamHandler",
            "formatter": "simple",
        },
        "logstash": {
            "level": "DEBUG" if DEBUG else "INFO",
            "class": "logstash.LogstashHandler",
            "host": LOGSTASH_HOST,
            "port": LOGSTASH_PORT,
            "message_type": "rodoviaria",
            "version": 1,
            "filters": ["correlation"],
        },
    },
    "loggers": {
        "rodoviaria": {
            "handlers": ["logstash"] if LOGSTASH_HOST and LOGSTASH_PORT else ["console"],
            "filters": ["correlation"],
            "level": "DEBUG" if DEBUG else "INFO",
            "propagate": False,
        },
        "django.db": {
            "handlers": ["console"],
            "level": "DEBUG" if DEBUG_SQL else "INFO",
            "propagate": False,
        },
    },
}

RESULT_BACKEND = config("RESULT_BACKEND", default=None)

SMARTBUS_API_ENVIRONMENT = config("SMARTBUS_API_ENVIRONMENT", default="demo")

CID_HEADER = "HTTP_X_CORRELATION_ID"

CSRF_TRUSTED_ORIGINS = [
    "https://rodoviaria.admin.buserdev.com.br",
    "https://rodoviariatest.admin.buserdev.com.br",
]
