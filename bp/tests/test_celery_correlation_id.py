import logging
from unittest import mock

import pytest
from celery import shared_task
from cid.locals import set_cid, get_cid
from django.test import TestCase

from bp.celery import RodoviariaTask, add_correlation_id_to_headers


@shared_task(base=RodoviariaTask, queue="test_queue")
def test_task():
    """Task de teste para verificar se o correlation_id está sendo propagado."""
    return get_cid()


class TestCeleryCorrelationId(TestCase):
    def test_add_correlation_id_to_headers_with_cid(self):
        """Testa se o correlation_id é adicionado aos headers quando existe um CID."""
        test_cid = "test-correlation-id-123"
        headers = {}
        
        # Simular contexto com correlation_id
        set_cid(test_cid)
        
        try:
            add_correlation_id_to_headers(headers)
            self.assertEqual(headers["correlation_id"], test_cid)
        finally:
            # Limpar o contexto
            set_cid(None)

    def test_add_correlation_id_to_headers_without_cid(self):
        """Testa se nenhum header é adicionado quando não há CID."""
        headers = {}
        
        # Garantir que não há correlation_id no contexto
        set_cid(None)
        
        add_correlation_id_to_headers(headers)
        self.assertNotIn("correlation_id", headers)

    def test_rodoviaria_task_sets_cid_from_headers(self):
        """Testa se a RodoviariaTask configura o CID a partir dos headers."""
        test_cid = "test-correlation-id-456"
        
        # Criar uma instância da task
        task = RodoviariaTask()
        task.request = {"correlation_id": test_cid}
        
        # Simular before_start
        task.before_start("test-task-id", [], {})
        
        # Verificar se o CID foi configurado
        self.assertEqual(get_cid(), test_cid)
        
        # Limpar o contexto
        set_cid(None)

    def test_rodoviaria_task_uses_task_id_as_fallback(self):
        """Testa se a RodoviariaTask usa o task_id como fallback quando não há correlation_id."""
        test_task_id = "test-task-id-789"
        
        # Criar uma instância da task sem correlation_id
        task = RodoviariaTask()
        task.request = {}
        
        # Simular before_start
        task.before_start(test_task_id, [], {})
        
        # Verificar se o task_id foi usado como CID
        self.assertEqual(get_cid(), test_task_id)
        
        # Limpar o contexto
        set_cid(None)

    @mock.patch('bp.celery.logger')
    def test_logging_includes_correlation_id(self, mock_logger):
        """Testa se os logs incluem o correlation_id quando configurado."""
        test_cid = "test-correlation-id-logging"
        
        # Configurar correlation_id
        set_cid(test_cid)
        
        try:
            # Simular um log
            logger = logging.getLogger("rodoviaria")
            logger.info("Test log message")
            
            # O filtro CorrelationIdContextFilter deve adicionar o correlation_id
            # Este teste verifica se o sistema está configurado corretamente
            current_cid = get_cid()
            self.assertEqual(current_cid, test_cid)
        finally:
            # Limpar o contexto
            set_cid(None)
